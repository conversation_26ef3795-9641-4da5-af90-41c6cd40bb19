import request from '@/utils/request'
// import qs from 'qs'
// 查询模板
export function get(params) {
  return request({
    url: 'api/extendThemeProperty',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/extendThemeProperty',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/extendThemeProperty',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/extendThemeProperty',
    method: 'put',
    data
  })
}

// 上传模板
export function upload(data) {
  return request({
    url: 'api/extendThemeProperty/upload',
    method: 'post',
    data
  })
}

// 获取option
export function getOption(params) {
  return request({
    url: 'api/extendThemeProperty/option',
    method: 'get',
    params
  })
}

// 上传模板
export function editOption(data) {
  return request({
    url: 'api/extendThemeProperty/option',
    method: 'post',
    data
  })
}

export default { get, add, edit, del, upload, getOption, editOption }
