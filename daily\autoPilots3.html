<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>自动驾驶3.0项目</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
<table>
    <thead>
    <tr>
        <th>序号</th>
        <th>路口编号</th>
        <th>施工分区</th>
        <th>路口名称</th>
        <th>区域</th>
        <th>经度</th>
        <th>维度</th>
        <th>状态</th>
    </tr>
    </thead>
    <tbody>
    #for(x : omAsset.queryAll(mixQuery.asset??,pageable??).content)
    <tr>
        <td>#(x.extend.data['0']??)</td>
        <td>#(x.fv4??)</td>
        <td>#(x.title??)</td>
        <td>#(x.fv6??)</td>
        <td>#(x.fv7??)</td>
        <td>#(x.fv2??)</td>
        <td>#(x.fv3??)</td>
        <td>#(x.status??)</td>
    </tr>
    #end
    </tbody>
</table>
</body>
</html>
