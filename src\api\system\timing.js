import request from '@/utils/request'

export function get(params) {
  return request({
    url: 'spider/api/jobs',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'spider/api/jobs',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'spider/api/jobs',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'spider/api/jobs',
    method: 'put',
    data
  })
}

export function updateIsPause(id) {
  return request({
    url: 'spider/api/jobs/' + id,
    method: 'put'
  })
}

export function execution(id) {
  return request({
    url: 'spider/api/jobs/exec/' + id,
    method: 'put'
  })
}

export function runSql(data) {
  return request({
    url: 'spider/api/jobs/run/fql',
    method: 'post',
    data
  })
}

export default { get, del, runSql, updateIsPause, execution, add, edit }
