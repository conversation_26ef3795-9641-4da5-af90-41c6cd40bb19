<!--分页-->
<template>
  <div>
    <el-pagination
      :page-size.sync="page.size"
      :total="page.total"
      :current-page.sync="page.page"
      :page-sizes="page.pageSizes"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="handleCurrentChange()"
      @current-change="handleCurrentChange()"
    />
  </div>

</template>
<script>
export default {
  name: 'Paging',
  props: {
    page: {
      type: Object,
      default() {
        return {
          size: 10,
          page: 0,
          total: 0,
          pageSizes: [10, 20, 30, 40, 50, 100]
        }
      }
    }
  },
  methods: {
    handleCurrentChange(e) {
      this.$emit('handleCurrentChange', e);
    }
  }
};
</script>
