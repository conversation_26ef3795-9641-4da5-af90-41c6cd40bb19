<template>
  <div>
    <el-form
      ref="generateForm"
      :label-position="data.config.labelPosition"
      :label-width="data.config.labelWidth + 'px'"
      :model="models"
      :rules="rules"
      :size="data.config.size"
      label-suffix=":"
    >
      <template v-for="item in data.list">
        <template v-if="item.type == 'grid'">
          <el-row
            :key="item.key"
            :align="item.options.align"
            :gutter="item.options.gutter ? item.options.gutter : 0"
            :justify="item.options.justify"
            type="flex"
          >
            <el-col v-for="(col, colIndex) in item.columns" :key="colIndex" :span="col.span">
              <template v-for="citem in col.list">
                <el-form-item
                  v-if="citem.type == 'blank'"
                  :key="citem.key"
                  :label="citem.name"
                  :prop="citem.model"
                >
                  <slot :model="models" :name="citem.model" />
                </el-form-item>
                <genetate-form-item
                  v-else
                  :key="citem.key"
                  :data="data"
                  :models.sync="models"
                  :preview="preview"
                  :prop-value="citem.model"
                  :remote="remote"
                  :widget="citem"
                  @input-change="onInputChange"
                />
              </template>
            </el-col>
          </el-row>
        </template>

        <template v-else-if="item.type == 'blank'">
          <el-form-item :key="item.key" :label="item.name" :prop="item.model">
            <slot :model="models" :name="item.model" />
          </el-form-item>
        </template>
        <!-- 子表单 -->
        <template v-else-if="item.type === 'subform'">
          <el-form-item
            :key="item.key"
            :label="!item.options.labelWidthStatus ? '' : item.name"
            :label-width="!item.options.labelWidthStatus ? '0px' : item.options.labelWidth + 'px'"
          >
            <el-table
              :data="models[item.model]"
              :header-cell-style="{ padding: '5px 0' }"
              border
              size="mini"
              style="width: 100%"
            >
              <el-table-column v-if="!preview && item.options.isAddSubfrom == 'yes'" fixed width="50">
                <template slot="header">
                  <i
                    class="el-icon-circle-plus"
                    style="font-size: 25px; color: #409EFF;cursor:pointer;"
                    @click="addSubformCol(item)"
                  />
                </template>
                <template slot-scope="scope">
                  <i
                    class="el-icon-remove"
                    style="font-size: 25px; color: red"
                    @click="delSubformCol(item, scope.$index)"
                  />
                </template>

              </el-table-column>
              <template v-for="(c, i) in item.columns">
                <div :key="i">
                  <el-table-column
                    v-for="v in c.list"
                    :key="v.key"
                    :label="v.name"
                    :prop="v.model"
                    min-width="250"
                  >
                    <template slot-scope="scope">
                      <genetate-form-item
                        :data="data"
                        :disabled="disabled"
                        :is-label="false"
                        :models.sync="models"
                        :preview="preview"
                        :prop-value="item.model + '.' + scope.$index + '.' + v.model"
                        :remote="remote"
                        :subform-index="scope.$index"
                        :subform-model="item.model"
                        :widget="v"
                      />
                    </template>
                  </el-table-column>
                </div>
              </template>
            </el-table>
          </el-form-item>
        </template>
        <!--辅助字段-->

        <template v-else-if="item.type === 'auxiliary'" />

        <template v-else>
          <genetate-form-item
            :key="item.key"
            :data="data"
            :disabled="disabled"
            :models.sync="models"
            :preview="preview"
            :prop-value="item.model"
            :remote="remote"
            :widget="item"
            @input-change="onInputChange"
          />
        </template>

      </template>
    </el-form>
  </div>
</template>

<script>
import GenetateFormItem from './GenerateFormItem'

export default {
  name: 'FmGenerateForm',
  components: {
    GenetateFormItem
  },
  /* eslint-disable */
	props: ['data', 'remote', 'value', 'insite', 'disabled', 'preview'],
	data() {
		return {
			tableData: [],
			models: {},
			rules: {},
			subformFields: {}
		}
	},
	watch: {
		data: {
			deep: true,
			handler(val) {
				// 为了解决BUG,注释掉 有时间细看
				// this.generateModle(val.list)
			}
		},
		value: {
			deep: true,
			immediate: true,
			handler(val) {
				console.log(val, '<==222=>', 'val')
				if (val && typeof val === 'object') {
					// 完全替换 models 而不是合并，确保数据更新
					this.models = { ...val }
					// 强制触发响应式更新
					this.$nextTick(() => {
						this.$forceUpdate()
					})
				}
			}
		}
	},
	created() {
		this.generateModle(this.data.list)
	},
	mounted() {
		this.isAddSbuformCol();
	},
	methods: {
		// 是否首次调用添加一行
		isAddSbuformCol() {
			this.data.list.reduce((pre, cur) => {
				if (cur.type == "subform") {
					if (cur.options.isAddSubfrom == "no") {
						this.addSubformCol(cur);
					}
				}
			})
		},
		addSubformCol(item) {
			var subformFields = {}
			for (var c of item.columns) {
				for (var l of c.list) {
					if (l.options !== null && l.options !== undefined) {
						subformFields[l.model] = l.options.defaultValue !== undefined && l.options.defaultValue !== null ? l.options.defaultValue : ""
					} else {
						subformFields[l.model] = ""
					}

					if (this.rules[item.model] === undefined) {
						this.rules[item.model] = []
					}
					if (this.rules[item.model][this.models[item.model].length] === undefined) {
						this.rules[item.model][this.models[item.model].length] = {}
					}
					this.rules[item.model][this.models[item.model].length][l.model] = [...l.rules.map(item => {
						if (item.pattern) {
							return { ...item, pattern: eval(item.pattern) }
						} else {
							return { ...item }
						}
					})]
				}
			}
			this.models[item.model].push(subformFields)
			if (item.options.isAddSubfrom == 'no') {
				this.models[item.model].length = 1
			}
			this.models.status = 1
		},
		delSubformCol(item, index) {
			this.models[item.model].splice(index, 1)
			let data = this.models[item.model];
			this.models[item.model] = [];
			this.$nextTick(() => {
				this.models[item.model] = data;
			})
			this.models.status = -1
		},
		generateModle(genList) {
			for (let i = 0; i < genList.length; i++) {
				if (genList[i].type === 'grid') {
					genList[i].columns.forEach(item => {
						this.generateModle(item.list)
					})
				} else {
					if (this.value && Object.keys(this.value).indexOf(genList[i].model) >= 0) {
						this.models[genList[i].model] = this.value[genList[i].model]
					} else {
						if (genList[i].type === 'blank') {
							this.$set(this.models, genList[i].model, genList[i].options.defaultType === 'String' ? '' : (genList[i].options.defaultType === 'Object' ? {} : []))
						}
						if (genList[i].type === 'subform') {
							this.$set(this.models, genList[i].model, [])
						} else {
							this.models[genList[i].model] = genList[i].options.defaultValue
						}
					}

					if (!this.preview) {
						if (this.rules[genList[i].model]) {
							this.rules[genList[i].model] = [...this.rules[genList[i].model], ...genList[i].rules.map(item => {
								if (item.pattern) {
									return { ...item, pattern: eval(item.pattern) }
								} else {
									return { ...item }
								}
							})]
						} else {
							this.rules[genList[i].model] = [...genList[i].rules.map(item => {
								if (item.pattern) {
									return { ...item, pattern: eval(item.pattern) }
								} else {
									return { ...item }
								}
							})]
						}
					}
				}
			}
		},
		getData() {
			return new Promise((resolve, reject) => {
				this.$refs.generateForm.validate(valid => {
					if (valid) {
						resolve(this.models)
					} else {
						reject(new Error(this.$t('fm.message.validError')).message)
					}
				})
			})
		},
		reset() {
			this.$refs.generateForm.resetFields()
		},
		onInputChange(value, field) {
			this.$emit('on-change', field, value, this.models)
		},
		refresh() {

		}
	}
}
</script>

<style lang="scss">
// @import '../styles/cover.scss';</style>
