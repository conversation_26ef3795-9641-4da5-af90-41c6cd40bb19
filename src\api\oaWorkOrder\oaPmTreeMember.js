import request from '@/utils/request'
import qs from 'qs'

export function get(params) {
  return request({
    url: 'api/oaPmTreeMember' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: 'api/oaPmTreeMember',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/oaPmTreeMember',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/oaPmTreeMember',
    method: 'put',
    data
  })
}

export default { get, add, edit, del }
