<template>
  <el-select
    v-model="currentSelects"
    :collapse-tags="collapse(currentSelects)"
    :filter-method="remoteSearch"
    :filterable="filterable"
    :placeholder="placeholder"
    multiple
    size="small"
    v-bind="$attrs"
    @change="handleSelect"
  >
    <el-checkbox-group v-model="currentSelects">
      <el-option
        v-for="item in options"
        :key="item[customKey]"
        :disabled="item.disabled"
        :label="item[customLabel]"
        :value="item[customValue]"
      >
        <el-checkbox
          :disabled="item.disabled"
          :label="item[customValue]"
          style="pointer-events: none"
        >
          {{ item[customLabel] }}
        </el-checkbox>
      </el-option>
    </el-checkbox-group>
  </el-select>
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
    customLabel: {
      type: String,
      default: 'label'
    },
    customValue: {
      type: String,
      default: 'value'
    },
    customKey: {
      type: String,
      default: 'id'
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    selectValue: {
      type: Array,
      default: () => []
    },
    tags: {
      type: Number,
      default: 2
    },
    filterable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentSelects: []
    };
  },
  computed: {
    collapse() {
      return function(value) {
        if (value && value.length > this.tags) {
          return true
        }
        return false
      }
    }
  },
  watch: {
    selectValue: {
      handler(newVal, oldVal) {
        this.currentSelects = newVal;
      },
      immediate: true, // 首次绑定后立即执行
      deep: true // 如果 selectValue 是对象或数组，需要设置 deep: true
    }
  },
  methods: {
    // 下拉框选择事件
    handleSelect(value) {
      this.currentSelects = value.length > 0 ? [...value] : [];
      this.$emit('selectChange', this.currentSelects);
    },
    // 远程搜索
    remoteSearch(query) {
      this.$emit('remoteSearch', query);
    }
  }
};
</script>

<style lang="scss" scoped></style>

