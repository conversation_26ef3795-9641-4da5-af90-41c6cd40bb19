import request from '@/utils/request'
import qs from 'qs'
export function getAmCategory(params) {
  // return request({
  //   url: 'api/extendCategory',
  //   method: 'get',
  //   params
  // })
  const url = 'api/amCategory';
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getAmCategorySuperior(ids) {
  const data = ids.length || ids.length === 0 ? ids : Array.of(ids)
  return request({
    url: 'api/amCategory/superior',
    method: 'post',
    data
  })
}
export function add(data) {
  return request({
    url: 'api/amCategory',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/amCategory',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/amCategory',
    method: 'put',
    data
  })
}

// 获取所有子级树
export function getChildren(data) {
  return request({
    url: 'api/amCategory/children',
    method: 'post',
    data
  })
}

// 更新模板
export function updateFormStruct(data) {
  return request({
    url: '/api/amCategory/update/formStruct',
    method: 'put',
    data
  })
}

// 更新绑定关系
export function updateRelation(data) {
  return request({
    url: 'api/amCategory/update/relation',
    method: 'put',
    data
  })
}
export default { getChildren, add, edit, del, getAmCategory, getAmCategorySuperior, updateFormStruct, updateRelation }
