import request from '@/utils/request'
import qs from 'qs';
const COMMONURL = 'api/oaPmProgress'

// 目录相关API
export function addCatalog(data) {
  return request({
    url: `${COMMONURL}/addCatalog`,
    method: 'post',
    data
  })
}

export function delCatalog(data) {
  return request({
    url: `${COMMONURL}/delCatalog`,
    method: 'post',
    data
  })
}

export function editCatalog(data) {
  return request({
    url: `${COMMONURL}/editCatalog`,
    method: 'post',
    data
  })
}

// 里程碑相关API
export function addMilestone(data) {
  return request({
    url: `${COMMONURL}/addMilestone`,
    method: 'post',
    data
  })
}

export function delMilestone(data) {
  return request({
    url: `${COMMONURL}/delMilestone`,
    method: 'post',
    data
  })
}

export function editMilestone(data) {
  return request({
    url: `${COMMONURL}/editMilestone`,
    method: 'post',
    data
  })
}

export function getMilestoneList(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}/getMilestoneList?${query}`,
    method: 'get'
  })
}

// 点位相关API
export function addPoint(data) {
  return request({
    url: `${COMMONURL}/addPoint`,
    method: 'post',
    data
  })
}

export function delPoint(data) {
  return request({
    url: `${COMMONURL}/delPoint`,
    method: 'post',
    data
  })
}

export function editPoint(data) {
  return request({
    url: `${COMMONURL}/editPoint`,
    method: 'post',
    data
  })
}

// 管控点相关API
export function addStep(data) {
  return request({
    url: `${COMMONURL}/addStep`,
    method: 'post',
    data
  })
}

export function delStep(data) {
  return request({
    url: `${COMMONURL}/delStep`,
    method: 'post',
    data
  })
}

export function editStep(data) {
  return request({
    url: `${COMMONURL}/editStep`,
    method: 'post',
    data
  })
}

export function getStepList(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}/getStepList?${query}`,
    method: 'get'
  })
}

// 项目进度树
export function getCatalogTree(id) {
  return request({
    url: `${COMMONURL}/catalogTree/${id}`,
    method: 'get'
  })
}

// 导入进度管控文件
export function importPmProgress(data) {
  return request({
    url: `${COMMONURL}/importPmProgress`,
    method: 'post',
    data
  })
}

export default {
  addCatalog,
  delCatalog,
  editCatalog,
  addMilestone,
  delMilestone,
  editMilestone,
  getMilestoneList,
  addPoint,
  delPoint,
  editPoint,
  addStep,
  delStep,
  editStep,
  getStepList,
  getCatalogTree,
  importPmProgress
}
