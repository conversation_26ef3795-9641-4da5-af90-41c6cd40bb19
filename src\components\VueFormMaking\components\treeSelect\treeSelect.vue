<template>
  <div class="tree-select">
    <span v-if="preview">
      {{ treeLable }}
    </span>
    <treeselect
      v-else
      v-model="treeValue"
      :load-options="ishandelelayfun ? null : (remote ? remote[element.options.lazyFun] : null)"
      :options="element.options.treeLists"
      :placeholder="element.options.placeholder"
      :style="{width: element.options.width}"
      append-to-body
      z-index="9999"
    />
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'TreeSelect',
  components: { Treeselect },
  // eslint-disable-next-line vue/require-prop-types
  props: ['element', 'preview', 'dataModel', 'remote', 'ishandelelayfun'],
  data() {
    return {
      treeLable: '',
      treeValue: ''
    }
  },
  watch: {
    'treeValue': {
      deep: true,
      immediate: true,
      handler(val, oldval) {
        this.treeValue = val
        this.$emit('changeTree', val)
      }
    }
  },
  created() {
    this.treeValue = this.dataModel
    // 树组件做特殊处理
    if (this.element.type === 'treeSelect' && !this.ishandelelayfun) {
      if (this.element.options.remote && this.remote[this.element.options.remoteFunc]) {
        this.remote[this.element.options.remoteFunc]({ pid: this.dataModel }, (data) => {
          this.element.options.treeLists = data
        })
      }
    }
    if (this.preview) {
      const list = this.flattenTree(this.element.options.treeLists)
      this.treeLable = list.find(item => (item.id == this.dataModel)).label || '-'
    }
  },

  methods: {
    flattenTree(tree) {
      const flattenedArray = [];

      function flatten(node) {
        flattenedArray.push(node);
        if (node.children) {
          node.children.forEach(child => {
            flatten(child);
          });
        }
      }

      tree.forEach(node => {
        flatten(node);
      });

      return flattenedArray;
    },

    projectFun() {

    }
  }
}
</script>

<style scoped>

</style>
