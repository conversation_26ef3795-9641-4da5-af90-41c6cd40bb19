// vxeTable表格样式
.vxe-table {

  //表头
  .vxe-table--header-wrapper {
    .vxe-header--row {
      min-height: 60px !important;
      padding: 10px 0;

      .vxe-cell--title {
        //color: #364359;
        line-height: 16px;
        font-weight: 400;
        font-size: 12px;
        white-space: pre-wrap !important;
        vertical-align: top;
        overflow: auto;
      }
    }
  }

  .vxe-footer--column.col--ellipsis > .vxe-cell .vxe-cell--item {
    text-align: right !important;
  }

  //表尾合计
  .vxe-table--render-wrapper {

    .vxe-footer--row {
      background-color: #f5f7fa !important;

      .vxe-footer--column {
        font-size: 14px;
        color: #606266;
        font-weight: 700;
        text-align: right !important;
        font-family: Sans-serif;

        span {
          text-align: right !important;
        }
      }
    }
  }
}


.vTable-table .vxe-table .vxe-table--header .vxe-header--row .vxe-header--column .vxe-cell--title {
  white-space: pre-wrap;
  word-wrap: break-word;
}





