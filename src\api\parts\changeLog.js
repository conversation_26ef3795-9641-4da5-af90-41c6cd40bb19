import request from '@/utils/request'
// import qs from 'qs'
// 修改记录
export function get(params) {
  return request({
    url: 'api/omChangeLog',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/omChangeLog',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/omChangeLog',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/omChangeLog',
    method: 'put',
    data
  })
}

export default { get, add, edit, del }
