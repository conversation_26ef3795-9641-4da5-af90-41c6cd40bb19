<template>
  <el-dropdown v-if="checkPer(permissionList)" v-bind="dropdownProps" @command="handleMany">
    <el-button v-bind="buttonProps">
      {{ title }}<i class="el-icon-arrow-down el-icon--right" />
    </el-button>
    <el-dropdown-menu slot="dropdown">
      <template v-for="item in filteredManyOption">
        <el-dropdown-item
          :key="item.command"
          v-permission="item.permission"
          :command="composeValue(item, scope.row)"
        >
          {{ item.name }}
        </el-dropdown-item>
      </template>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import { getAllpermission } from '@/utils/permission';

export default {
  props: {
    // 业务
    title: {
      type: String,
      default: '更多操作'
    },
    manyOption: {
      type: Array,
      required: true
    },
    handleMany: {
      type: Function,
      required: true
    },
    showItem: {
      type: Function,
      required: false
    },
    scope: {
      type: Object,
      required: true
    },
    // 按钮属性
    size: {
      type: String,
      default: 'mini'
    },
    type: {
      type: String,
      default: 'primary'
    },
    plain: {
      type: <PERSON><PERSON>an,
      default: false
    },
    // 下拉菜单属性
    trigger: {
      type: String,
      default: 'hover'
    },
    placement: {
      type: String,
      default: 'bottom-end'
    }
  },
  computed: {
    filteredManyOption({ manyOption, scope, showItem }) {
      return manyOption.filter(item => {
        if (showItem && typeof showItem === 'function') {
          return showItem(item, scope.row);
        }
        return true;
      });
    },
    buttonProps({ size, type, plain }) {
      return { plain, size, type };
    },
    dropdownProps({ trigger, placement }) {
      return { trigger, placement };
    },
    permissionList({ filteredManyOption }) {
      return getAllpermission(filteredManyOption)
    }
  },
  methods: {
    composeValue(item, row) {
      return {
        command: Number(item.command),
        row,
        item
      }
    }
  }
};
</script>

<style>
/* 样式 */
</style>
