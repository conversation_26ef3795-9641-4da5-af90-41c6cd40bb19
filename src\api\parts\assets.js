import request from '@/utils/request'
import qs from 'qs'

// 资产管理
export function get(params) {
  return request({
    url: 'api/omAsset' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getOmAssetSmall(params, congig = {}) {
  return request({
    url: 'api/omAsset/small' + '?' + qs.stringify(params, { indices: false }),
    method: 'get',
    ...congig
  })
}

export function getDetailWithX(params, config = {}) {
  return request({
    url: 'api/omAsset/detailWithX' + '?' + qs.stringify(params, { indices: false }),
    method: 'get',
    ...config
  })
}

export function getInspectionWithX(params) {
  return request({
    url: 'api/omAsset/inspectionWithX' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function toRedisGeoIndex(params) {
  return request({
    url: '/api/omAsset/toRedisGeoIndex',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/omAsset',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/omAsset',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/omAsset',
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: 'api/omAsset/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'api/omAsset/update/relation',
    method: 'put',
    data
  })
}

export function findAssetWithTagAndTop(params, congfig = {}) {
  const { size, sort, page } = params
  const pageParams = {
    size: size,
    sort: sort,
    page: page
  }
  return request({
    url: 'api/omAsset/findAssetWithTagAndTop' + '?' + qs.stringify(pageParams, { indices: false }),
    method: 'post',
    data: params,
    ...congfig
  })
}

export function getOmAssetAutoPilot(params, congfig = {}) {
  return request({
    url: 'api/omAsset/autoPilot' + '?' + qs.stringify(params, { indices: false }),
    method: 'get',
    ...congfig
  })
}

export function empty(ids) {
  return request({
    url: 'api/omAsset/empty',
    method: 'delete',
    data: ids
  })
}

export default {
  getDetailWithX,
  getInspectionWithX,
  getOmAssetSmall,
  get,
  add,
  edit,
  del,
  updateFormStruct,
  updateRelation,
  toRedisGeoIndex,
  findAssetWithTagAndTop,
  getOmAssetAutoPilot,
  empty
}
