import request from '@/utils/request'
import qs from 'qs'

export function get(params) {
  return request({
    url: 'spider/api/spiderReadAttention' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getSmall(params) {
  return request({
    url: 'spider/api/spiderReadAttention/small' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: 'spider/api/spiderReadAttention',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'spider/api/spiderReadAttention',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'spider/api/spiderReadAttention',
    method: 'put',
    data
  })
}

export function cancel(data) {
  return request({
    url: 'spider/api/spiderReadAttention/cancel',
    method: 'delete',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: 'spider/api/spiderReadAttention/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'spider/api/spiderReadAttention/update/relation',
    method: 'put',
    data
  })
}

export function importRule(data) {
  return request({
    url: 'spider/api/spiderReadAttention/import/xls/rule',
    method: 'post',
    data
  })
}

export function affirm(data) {
  return request({
    url: 'spider/api/spiderReadAttention/affirm',
    method: 'post',
    data
  })
}

export default { get, add, edit, del, getSmall, affirm, cancel, updateFormStruct, updateRelation, importRule }
