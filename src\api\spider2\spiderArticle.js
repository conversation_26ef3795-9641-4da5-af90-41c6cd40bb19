import request from '@/utils/request'
import qs from 'qs'

export function get(params) {
  return request({
    url: 'api/spiderArticle' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getManySmall(params) {
  return request({
    url: 'api/spiderArticle/small' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: 'api/spiderArticle',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/spiderArticle',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/spiderArticle',
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: 'api/spiderArticle/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'api/spiderArticle/update/relation',
    method: 'put',
    data
  })
}

export function importRule(data) {
  return request({
    url: 'api/spiderArticle/import/xls/rule',
    method: 'post',
    data
  })
}

export default { get, add, edit, del, getManySmall, updateFormStruct, updateRelation, importRule }
