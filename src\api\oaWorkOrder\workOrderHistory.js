import request from '@/utils/request'
import qs from 'qs';
// 工单管理--流转历史
const COMMONURL = 'api/oaWorkOrderHistory'

export function getCirculationHistory(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}?${query}`,
    method: 'get'
  })
}

export function addCirculationHistory(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'post',
    data
  })
}

export function delCirculationHistory(ids) {
  return request({
    url: `${COMMONURL}`,
    method: 'delete',
    data: ids
  })
}

export function editCirculationHistory(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'put',
    data
  })
}

export default { getCirculationHistory, addCirculationHistory, editCirculationHistory, delCirculationHistory }
