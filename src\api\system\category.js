import request from '@/utils/request'
import qs from 'qs'
export function getCategory(params) {
  // return request({
  //   url: 'api/extendCategory',
  //   method: 'get',
  //   params
  // })
  const url = 'api/extendCategory';
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getCategorySuperior(ids) {
  const data = ids.length || ids.length === 0 ? ids : Array.of(ids)
  return request({
    url: 'api/extendCategory/superior',
    method: 'post',
    data
  })
}
export function add(data) {
  return request({
    url: 'api/extendCategory',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/extendCategory',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/extendCategory',
    method: 'put',
    data
  })
}

// 获取所有子级树
export function getChildren(data) {
  return request({
    url: 'api/extendCategory/children',
    method: 'post',
    data
  })
}

// 更新模板
export function updateFormStruct(data) {
  return request({
    url: '/api/extendCategory/update/formStruct',
    method: 'put',
    data
  })
}

// 更新绑定关系
export function updateRelation(data) {
  return request({
    url: 'api/extendCategory/update/relation',
    method: 'put',
    data
  })
}
export default { getChildren, add, edit, del, getCategory, getCategorySuperior, updateFormStruct, updateRelation }
