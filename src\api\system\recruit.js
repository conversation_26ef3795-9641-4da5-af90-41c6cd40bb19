import request from '@/utils/request'
// import qs from 'qs'
// 招采信息管理

export function get(params) {
  return request({
    url: 'api/siteBizInfo',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/siteBizInfo',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/siteBizInfo',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/siteBizInfo',
    method: 'put',
    data
  })
}

export function preRequest(data) {
  return request({
    url: 'api/extendCommonContent/preRequest',
    method: 'post',
    data
  })
}

export default { get, add, edit, del, preRequest }
