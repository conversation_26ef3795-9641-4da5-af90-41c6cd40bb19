<template>
  <div class="widget-form-container">
    <div v-if="data.list.length == 0" class="form-empty">{{ $t('fm.description.containerEmpty') }}</div>
    <el-form
      :label-position="data.config.labelPosition"
      :label-width="data.config.labelWidth + 'px'"
      :size="data.config.size"
      label-suffix=":"
    >
      <draggable
        v-model="data.list"
        class=""
        v-bind="{group:'people', ghostClass: 'ghost',animation: 200, handle: '.drag-widget'}"
        @add="handleWidgetAdd"
        @end="handleMoveEnd"
      >

        <transition-group class="widget-form-list" name="fade" tag="div">
          <template v-for="(element, index) in data.list">
            <!-- 珊格 -->
            <template v-if="element.type == 'grid'">
              <el-row
                v-if="element && element.key"
                :key="element.key"
                :align="element.options.align"
                :class="{active: selectWidget.key == element.key}"
                :gutter="element.options.gutter ? element.options.gutter : 0"
                :justify="element.options.justify"
                class="widget-col widget-view"
                type="flex"
                @click.native="handleSelectWidget(index)"
              >
                <el-col v-for="(col, colIndex) in element.columns" :key="colIndex" :span="col.span ? col.span : 0">

                  <draggable
                    v-model="col.list"
                    :no-transition-on-drag="true"
                    v-bind="{group:'people', ghostClass: 'ghost',animation: 200, handle: '.drag-widget'}"
                    @add="handleWidgetColAdd($event, element, colIndex)"
                    @end="handleMoveEnd"
                  >
                    <transition-group class="widget-col-list" name="fade" tag="div">
                      <template v-for="(el, i) in col.list">
                        <widget-form-item
                          v-if="el.key"
                          :key="el.key"
                          :data="col"
                          :data-config="data"
                          :element="el"
                          :index="i"
                          :select.sync="selectWidget"
                        />
                      </template>

                    </transition-group>

                  </draggable>
                </el-col>
                <div v-if="selectWidget.key == element.key" class="widget-view-action widget-col-action">

                  <i class="iconfont icon-trash" @click.stop="handleWidgetDelete(index)" />
                </div>

                <div v-if="selectWidget.key == element.key" class="widget-view-drag widget-col-drag">
                  <i class="iconfont icon-drag drag-widget" />
                </div>
              </el-row>
            </template>
            <!-- 子表单 -->
            <template v-else-if="element.type == 'subform'">
              <el-row
                v-if="element && element.key"
                :key="element.key"
              >
                <el-form-item
                  :class="{active: selectWidget.key === element.key, 'is_req': element.options.required}"
                  :label="element.options.labelWidthStatus?element.name:''"
                  :label-width="element.options.labelWidthStatus?data.config.labelWidth + 'px': '0px'"
                  class="widget-col widget-view"
                  @click.native="handleSelectWidget(index)"
                >
                  <div
                    :align="element.options.align"
                    :class="{active: selectWidget.key == element.key}"
                    :gutter="element.options.gutter ? element.options.gutter : 0"
                    :justify="element.options.justify"
                    type="flex"
                  >
                    <el-col v-for="(col, colIndex) in element.columns" :key="colIndex" :span="col.span ? col.span : 0">
                      <draggable
                        v-model="col.list"
                        :no-transition-on-drag="true"
                        v-bind="{group:'people', ghostClass: 'ghost',animation: 200, handle: '.drag-widget'}"
                        @add="handleWidgetColAdd($event, element, colIndex)"
                        @end="handleMoveEnd"
                      >
                        <transition-group
                          class="widget-col-list"
                          name="fade"
                          style="min-height: 131px;overflow-x: auto; white-space: nowrap;"
                          tag="div"
                        >
                          <template v-for="(el, i) in col.list">
                            <div
                              v-if="el && el.key"
                              :key="el.key"
                              @click="handleSelectWidget(i)"
                            >
                              <widget-form-item
                                :data="col"
                                :data-config="data"
                                :element="el"
                                :index="i"
                                :is-label="true"
                                :is-table="true"
                                :select.sync="selectWidget"
                              />
                            </div>
                          </template>
                        </transition-group>
                      </draggable>
                    </el-col>

                    <div v-if="selectWidget.key == element.key" class="widget-view-action widget-col-action">
                      <i class="iconfont icon-trash" @click.stop="handleWidgetDelete(index)" />
                    </div>

                    <div v-if="selectWidget.key == element.key" class="widget-view-drag widget-col-drag">
                      <i class="iconfont icon-drag drag-widget" />
                    </div>
                  </div>
                </el-form-item>
              </el-row>
            </template>
            <template v-else>
              <widget-form-item
                v-if="element && element.key"
                :key="element.key"
                :data="data"
                :data-config="data"
                :element="element"
                :index="index"
                :select.sync="selectWidget"
              />
            </template>
          </template>
        </transition-group>
      </draggable>
    </el-form>
  </div>
</template>

<script>
import Draggable from 'vuedraggable'
import WidgetFormItem from './WidgetFormItem'

export default {
  components: {
    Draggable,
    WidgetFormItem
  },
  /* eslint-disable */
  props: ['data', 'select'],
  data() {
    return {
      selectWidget: this.select
    }
  },
  watch: {
    select(val) {
      this.selectWidget = val
    },
    selectWidget: {
      handler(val) {
        this.$emit('update:select', val)
      },
      deep: true
    }
  },
  mounted() {
    document.body.ondrop = function (event) {
      const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1
      if (isFirefox) {
        event.preventDefault()
        event.stopPropagation()
      }
    }
  },
  methods: {
    handleMoveEnd({newIndex, oldIndex}) {
    },
    handleSelectWidget(index) {
      this.selectWidget = this.data.list[index]
    },
    handleWidgetAdd(evt) {
      const newIndex = evt.newIndex
      const to = evt.to

      // 为拖拽到容器的元素添加唯一 key
      const key = Date.parse(new Date()) + '_' + Math.ceil(Math.random() * 99999)
      this.$set(this.data.list, newIndex, {
        ...this.data.list[newIndex],
        options: {
          ...this.data.list[newIndex].options,
          remoteFunc: 'func_' + key,
          lazyFun: 'lazyFun'
        },
        key,
        // 绑定键值
        model: this.data.list[newIndex].type + '_' + key,
        rules: []
      })

      if (this.data.list[newIndex].type === 'radio' || this.data.list[newIndex].type === 'checkbox' || this.data.list[newIndex].type === 'select') {
        this.$set(this.data.list, newIndex, {
          ...this.data.list[newIndex],
          options: {
            ...this.data.list[newIndex].options,
            options: this.data.list[newIndex].options.options.map(item => ({
              ...item
            }))
          }
        })
      }

      if (this.data.list[newIndex].type === 'grid' || this.data.list[newIndex].type === 'subform') {
        this.$set(this.data.list, newIndex, {
          ...this.data.list[newIndex],
          columns: this.data.list[newIndex].columns.map(item => ({...item}))
        })
      }

      this.selectWidget = this.data.list[newIndex]
    },
    handleWidgetColAdd($event, row, colIndex) {
      const newIndex = $event.newIndex
      const oldIndex = $event.oldIndex
      const item = $event.item

      // 防止布局元素的嵌套拖拽
      if (item.className.indexOf('data-grid') >= 0) {
        // 如果是列表中拖拽的元素需要还原到原来位置
        item.tagName === 'DIV' && this.data.list.splice(oldIndex, 0, row.columns[colIndex].list[newIndex])

        row.columns[colIndex].list.splice(newIndex, 1)

        return false
      }

      const key = Date.parse(new Date()) + '_' + Math.ceil(Math.random() * 99999)

      this.$set(row.columns[colIndex].list, newIndex, {
        ...row.columns[colIndex].list[newIndex],
        options: {
          ...row.columns[colIndex].list[newIndex].options,
          remoteFunc: 'func_' + key
        },
        key,
        // 绑定键值
        model: row.columns[colIndex].list[newIndex].type + '_' + key,
        rules: []
      })

      if (row.columns[colIndex].list[newIndex].type === 'radio' || row.columns[colIndex].list[newIndex].type === 'checkbox' || row.columns[colIndex].list[newIndex].type === 'select') {
        this.$set(row.columns[colIndex].list, newIndex, {
          ...row.columns[colIndex].list[newIndex],
          options: {
            ...row.columns[colIndex].list[newIndex].options,
            options: row.columns[colIndex].list[newIndex].options.options.map(item => ({
              ...item
            }))
          }
        })
      }

      this.selectWidget = row.columns[colIndex].list[newIndex]
    },
    handleWidgetDelete(index) {
      if (this.data.list.length - 1 === index) {
        if (index === 0) {
          this.selectWidget = {}
        } else {
          this.selectWidget = this.data.list[index - 1]
        }
      } else {
        this.selectWidget = this.data.list[index + 1]
      }

      this.$nextTick(() => {
        this.data.list.splice(index, 1)
      })
    }
  }
}
</script>
