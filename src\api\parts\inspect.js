import request from '@/utils/request'
// import qs from 'qs'
// 巡检管理
export function get(params) {
  return request({
    url: 'api/omInspect',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/omInspect',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/omInspect',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/omInspect',
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: 'api/omInspect/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'api/omInspect/update/relation',
    method: 'put',
    data
  })
}

export default { get, add, edit, del, updateFormStruct, updateRelation }
