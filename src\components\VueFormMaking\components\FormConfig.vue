<template>
  <div class="form-config-container">
    <el-form label-position="top">

      <el-form-item :label="$t('fm.config.form.name')">
        <el-input v-model="data.name" />
      </el-form-item>
      <el-form-item :label="$t('fm.config.form.labelPosition.title')">
        <el-radio-group v-model="data.labelPosition">
          <el-radio-button label="left">{{ $t('fm.config.form.labelPosition.left') }}</el-radio-button>
          <el-radio-button label="right">{{ $t('fm.config.form.labelPosition.right') }}</el-radio-button>
          <el-radio-button label="top">{{ $t('fm.config.form.labelPosition.top') }}</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item :label="$t('fm.config.form.labelWidth')">
        <el-input-number v-model="data.labelWidth" :max="200" :min="0" :step="10" />
      </el-form-item>

      <el-form-item :label="$t('fm.config.form.size')">
        <el-radio-group v-model="data.size">
          <el-radio-button label="medium">medium</el-radio-button>
          <el-radio-button label="small">small</el-radio-button>
          <el-radio-button label="mini">mini</el-radio-button>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  /* eslint-disable */
	props: ['data']
}
</script>
