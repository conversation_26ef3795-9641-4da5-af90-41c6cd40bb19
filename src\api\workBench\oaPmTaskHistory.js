import request from '@/utils/request'
import qs from 'qs';

const COMMONURL = 'api/oaPmTaskHistory'

export function getTaskHistory(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}?${query}`,
    method: 'get'
  })
}

export function addTaskHistory(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'post',
    data
  })
}

export function delTaskHistory(ids) {
  return request({
    url: `${COMMONURL}`,
    method: 'delete',
    data: ids
  })
}

export function editTaskHistory(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'put',
    data
  })
}

export default { getTaskHistory, addTaskHistory, editTaskHistory, delTaskHistory }
