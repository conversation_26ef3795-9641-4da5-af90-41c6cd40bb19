import request from '@/utils/request'
import qs from 'qs';
// 项目目录关系对照管理
const COMMONURL = 'api/oaPmCatalogRelation'

export function get(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}?${query}`,
    method: 'get'
  })
}

export function getSmall(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}/small?${query}`,
    method: 'get'
  })
}

export function modifiedCatalog(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}/modifiedCatalog?${query}`,
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: `${COMMONURL}`,
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: `${COMMONURL}/update/formStruct`,
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: `${COMMONURL}/update/relation`,
    method: 'put',
    data
  })
}

export function importRule(data) {
  return request({
    url: `${COMMONURL}/import/xls/rule`,
    method: 'post',
    data
  })
}

export default {
  get,
  getSmall,
  add,
  edit,
  del,
  modifiedCatalog,
  updateFormStruct,
  updateRelation,
  importRule
}
