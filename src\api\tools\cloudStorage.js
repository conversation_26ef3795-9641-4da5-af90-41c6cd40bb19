import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/extendFileStorage',
    method: 'post',
    data
  })
}

export function create(data) {
  return request({
    url: 'api/extendFileStorage/create',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/extendFileStorage',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/extendFileStorage',
    method: 'put',
    data
  })
}

export function downloadFile(data) {
  const url = '/api/extendFileStorage/download';
  return request({
    url: url,
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 存储生成缩略图
export function addImage(data) {
  return request({
    url: 'api/extendFileStorage/image',
    method: 'post',
    data
  })
}

// 图片存储并按位置裁切
export function addImageCropPosition(data) {
  return request({
    url: 'api/extendFileStorage/image/crop/position',
    method: 'post',
    data
  })
}

// 图片存储并按比例缩放
export function addImageZoomScale(data) {
  return request({
    url: 'api/extendFileStorage/image/zoom/scale',
    method: 'post',
    data
  })
}

// 图片存储并按尺寸缩放
export function addImageZoomSize(data) {
  return request({
    url: 'api/extendFileStorage/image/zoom/size',
    method: 'post',
    data
  })
}

export default {
  create,
  add,
  edit,
  del,
  downloadFile,
  addImage,
  addImageCropPosition,
  addImageZoomScale,
  addImageZoomSize
}
