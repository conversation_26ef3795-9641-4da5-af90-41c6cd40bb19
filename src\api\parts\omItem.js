import request from '@/utils/request'
// import qs from 'qs'
// 别名管理
export function get(params) {
  return request({
    url: 'api/omItem',
    method: 'get',
    params
  })
}

export function getSmall(params) {
  return request({
    url: 'api/omItem/small',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/omItem',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/omItem',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/omItem',
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: 'api/omItem/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'api/omItem/update/relation',
    method: 'put',
    data
  })
}

export function updateAssetId(data) {
  return request({
    url: 'api/omItem/update/assetId',
    method: 'put',
    data
  })
}

export default { get, getSmall, add, edit, del, updateFormStruct, updateRelation, updateAssetId }
