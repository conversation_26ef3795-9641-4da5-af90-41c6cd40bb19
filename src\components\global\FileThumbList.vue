<template>
  <div class="thumbnail-view">
    <div v-if="urls.length > 0" class="thumbnail-images">
      <el-image
        v-for="(url, index) in urls"
        :key="index"
        :src="url"
        :preview-src-list="previewList"
        class="el-avatar"
        fit="contain"
        lazy
        v-bind="$attrs"
      >
        <div slot="error">
          <i class="el-icon-document" />
        </div>
      </el-image>
    </div>
    <div v-else class="el-avatar">
      <img
        v-if="findFileTypeByExtension()"
        :src="require(`@/assets/file_images/${findFileTypeByExtension()}.png`)"
        alt=""
        @click="preView()"
      >
      <i v-else class="el-icon-document" @click="preView()" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'FileThumbList',
  props: {
    urls: {
      type: Array,
      default: () => []
    },
    previewList: {
      type: Array,
      default: () => []
    },
    fileExt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileType: {
        excel: ['xlsx', 'xls'],
        word: ['doc', 'docx'],
        ppt: ['ppt', 'pptx'],
        pdf: ['pdf']
      }
    }
  },
  computed: {
    findFileTypeByExtension() {
      if (!this.fileExt) {
        return '';
      }
      for (const key in this.fileType) {
        if (this.fileType[key].includes(this.fileExt.toLowerCase())) {
          return key;
        }
      }
      return ''; // 如果没有找到匹配项，则返回默认值
    }
  },
  methods: {
    preView() {
      this.$emit('preView', this.previewList[0])
    }
  }
}
</script>

<style lang="scss" scoped>
.thumbnail-view {
  display: flex;
  flex-wrap: wrap;
}
.thumbnail-images {
  display: flex;
  flex-wrap: wrap;
}
.el-avatar {
  width: 50px;
  height: 50px;
  margin: 5px;
}
</style>
