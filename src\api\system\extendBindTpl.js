import request from '@/utils/request'
// import qs from 'qs'
// 查询模板
export function get(params) {
  return request({
    url: 'api/extendBindTpl',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/extendBindTpl',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/extendBindTpl',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/extendBindTpl',
    method: 'put',
    data
  })
}

export function findAllEntity(params) {
  return request({
    url: 'api/extendBindTpl/findAllEntity',
    method: 'get',
    params
  })
}

export function findAllField(data) {
  return request({
    url: 'api/extendBindTpl/findAllField',
    method: 'post',
    data
  })
}

export default { get, add, edit, del, findAllEntity, findAllField }
