<template>
  <span class="update-button">
    <el-button
      v-if="permission.updateT"
      v-permission="permission.updateT"
      class="filter-item"
      icon="el-icon-refresh"
      size="mini"
      type="primary"
      @click="updateTemplate"
    >更新模板</el-button>
    <el-button
      v-if="permission.updateR"
      v-permission="permission.updateR"
      class="filter-item"
      icon="el-icon-refresh"
      size="mini"
      type="success"
      @click="updateRelation"
    >更新绑定</el-button>
    <el-button
      v-if="permission.updateA"
      v-permission="permission.updateA"
      class="filter-item"
      icon="el-icon-refresh"
      size="mini"
      type="warning"
      @click="updateAssetId"
    >更新部件id</el-button>
    <el-button
      v-if="permission.updateG"
      v-permission="permission.updateG"
      class="filter-item"
      icon="el-icon-refresh"
      size="mini"
      type="warning"
      @click="updateToRedis"
    >toRedis</el-button>
  </span>
</template>

<script>
export default {
  name: 'UpdateButton2',
  props: {
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    },
    bindId: {
      type: String,
      validator: function(value) {
        return Number(value)
      },
      default: ''
    },
    enabled: {
      type: [Array, Number],
      default: () => {
        [1]
      }
    },
    otherParams: {
      type: Object,
      default: () => {
        return {}
      }
    },
    categoryId: {
      type: String,
      default: ''
    },
    crudMethod: {
      type: Object,
      default: () => ({})
    },
    callbackFun: {
      type: Function,
      default: null
    }
  },
  methods: {
    updateTemplate() {
      if (this.crudMethod.updateFormStruct) {
        this.crudMethod.updateFormStruct({ bindId: Number(this.bindId), enabled: this.enabled, ...this.otherParams })
          .then(res => {
            this.successFun();
          })
      }
    },
    updateRelation() {
      if (this.crudMethod.updateRelation) {
        this.crudMethod.updateRelation({ bindId: Number(this.bindId), enabled: this.enabled, ...this.otherParams })
          .then(res => {
            this.successFun();
          })
      }
    },
    updateAssetId() {
      const json = {};
      if (this.categoryId) {
        json.categoryId = this.categoryId
      }
      if (this.crudMethod.updateAssetId) {
        this.crudMethod.updateAssetId(json)
          .then(res => {
            this.successFun();
          })
      }
    },
    updateToRedis() {
      if (this.crud.crudMethod.toRedisGeoIndex) {
        this.crud.crudMethod.toRedisGeoIndex()
          .then(res => {
            this.successFun();
          })
      }
    },
    successFun(title = '更新成功') {
      this.$notify({
        title,
        type: 'success',
        duration: 2500
      })
      setTimeout(() => {
        this.callbackFun && this.callbackFun();
      }, 500)
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.update-button {
	.el-button {
		margin-left: 0;
	}
}
</style>
