<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>自动驾驶-杆体数据</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
<table>
    <thead>
    <tr>
        <th>序号</th>
        <th>路口编号</th>
        <th>杆体编号</th>
        <th>路口名称</th>
    </thead>
    <tbody>
    #for(x : omAssetAffiliated.queryAllSmall(mixQuery.affiliated??,pageable??).content)
    <tr>
        <td>#(x.extend.data['0']??)</td>
        <td>#(x.fv1??)</td>
        <td>#(x.fv4??)</td>
        <td>#(x.title??)</td>
    </tr>
    #end
    </tbody>
</table>
</body>
</html>
