import request from '@/utils/request'
import qs from 'qs'

export function get(params) {
  return request({
    url: 'spider/api/spiderArticle' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getSmall(params) {
  return request({
    url: 'spider/api/spiderArticle/small' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: 'spider/api/spiderArticle',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'spider/api/spiderArticle',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'spider/api/spiderArticle',
    method: 'put',
    data
  })
}

export function relevance(data) {
  return request({
    url: 'spider/api/spiderArticle/relevance',
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: 'spider/api/spiderArticle/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'spider/api/spiderArticle/update/relation',
    method: 'put',
    data
  })
}

export function importRule(data) {
  return request({
    url: 'spider/api/spiderArticle/import/xls/rule',
    method: 'post',
    data
  })
}

export function getSpiderArticleBase(params) {
  return request({
    url: 'spider/api/spiderArticleBase' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function editSpiderArticleBase(data) {
  return request({
    url: 'spider/api/spiderArticleBase',
    method: 'put',
    data
  })
}

export default {
  get,
  add,
  edit,
  del,
  getSmall,
  relevance,
  updateFormStruct,
  updateRelation,
  importRule,
  getSpiderArticleBase,
  editSpiderArticleBase
}
