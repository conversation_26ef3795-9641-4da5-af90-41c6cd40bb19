<template>
  <div v-loading.fullscreen.lock="fullscreenLoading" element-loading-text="正在上传中...">
    <el-upload
      :action="`${element.options.action}?name=${''}&platform=${element.options.platform}`"
      :before-remove="beforeRemove"
      :before-upload="beforeUpload"
      :disabled="element.options.disabled"
      :file-list="dataModel"
      :headers="headers"
      :limit="element.options.length"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :style="{'width': element.options.width}"
      multiple
    >
      <div v-if="!preview">
        <el-button size="small" type="primary">点击上传</el-button>
        <div slot="tip" class="el-upload__tip">{{ element.options.tip }}</div>
      </div>
    </el-upload>
  </div>
</template>

<script>
import { downloadFile } from '@/utils/index'
import crudLocalStorage from '@/api/tools/localStorage'
import { getToken } from '@/utils/auth'

export default {
  name: 'FileUpload',
  // eslint-disable-next-line vue/require-prop-types
  props: ['element', 'preview', 'dataModel'],
  data() {
    return {
      fileListTmp: [],
      name: '',
      headers: { 'Authorization': getToken() },
      fullscreenLoading: false,
      uploading: false
    }
  },
  watch: {
    'dataModel': {
      deep: true,
      immediate: true,
      handler(val) {
        if (val && val.length) {
          console.log(val, '<===>', 'val')
          val.map(item => {
            if (item.filename && !item.name) {
              item.name = item.filename;
            }
          });
          this.fileListTmp = this.dataModel;
          console.log(this.fileListTmp, '<===>', 'this.fileListTmp')
        }
      }
    }
  },
  methods: {
    handleRemove(file, fileList) {
      this.fileListTmp = fileList
      this.$emit('fileList', fileList)
    },
    handlePreview(file) {
      const loading = this.$loading({
        lock: true,
        text: '下载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      if (file?.platform.indexOf('local') != -1) {
        crudLocalStorage.downloadFile(file.response).then((res) => {
          loading.close();
          downloadFile(res, file.name, file.type)
        }).catch(e => {
          loading.close();
        });
      } else {
        window.open(file.url)
        loading.close();
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(`最多允许上传 ${this.element.options.length} 个文件。`)
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定要移除 ${file.name}？`)
    },
    beforeUpload() {
      this.fullscreenLoading = true;
    },
    handleSuccess(response, file, fileList) {
      this.name = file.name;
      const allFilesUploaded = fileList.every(file => file.status === 'success');

      if (allFilesUploaded) {
        this.fullscreenLoading = false
        // 执行你的操作
      }
      this.fullscreenLoading = false;
      this.$emit('fileList', fileList)
    }
  }
}
</script>

<style scoped>

</style>
