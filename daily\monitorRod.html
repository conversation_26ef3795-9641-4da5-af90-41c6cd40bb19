<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">

<html>
<head>
	<meta charset="UTF-8">
	<meta http-equiv="content-type" content="text/html; charset=iso-8859-1"/>
	<title>监控杆信息</title>
	<meta name="generator" content="LibreOffice 5.4.7.2 (Linux)"/>
	<meta name="author" content="Microsoft Office User"/>
	<meta name="created" content="2021-06-04T01:30:47"/>
	<meta name="changedby" content="admin"/>
	<meta name="changed" content="2022-10-08T08:02:34"/>
	<meta name="AppVersion" content="16.0300"/>
	<meta name="DocSecurity" content="0"/>
	<meta name="HyperlinksChanged" content="false"/>
	<meta name="LinksUpToDate" content="false"/>
	<meta name="ScaleCrop" content="false"/>
	<meta name="ShareDoc" content="false"/>
	
	<style type="text/css">
		body,div,table,thead,tbody,tfoot,tr,th,td,p { font-family:"&#31561;&#32447;"; font-size:small }
		a.comment-indicator:hover + comment { background:#ffd; position:absolute; display:block; border:1px solid black; padding:0.5em;  } 
		a.comment-indicator { background:red; display:inline-block; border:1px solid black; width:0.5em; height:0.5em;  } 
		comment { display:none;  } 
    .td-tblr2 {
      border-top: 2px solid #000000;
      border-bottom: 1px solid #000000;
      border-left: 1px solid #000000;
      border-right: 1px solid #000000;
    }
    .td-tblr1 {
      border-top: 2px solid #000000;
      border-bottom: 1px solid #000000;
      border-left: 1px solid #000000;
      border-right: 1px solid #000000;
    }

    .td-blr {
      border-bottom: 1px solid #000000;
      border-left: 1px solid #000000;
      border-right: 1px solid #000000
    }

    .td-bl {
      border-bottom: 1px solid #000000;
      border-left: 1px solid #000000;
    }
	</style>
	
</head>

<body>
<table cellspacing="0" border="0">
	<colgroup width="435"></colgroup>
	<colgroup width="114"></colgroup>
	<colgroup width="644"></colgroup>
	<colgroup width="429"></colgroup>
	<colgroup width="103"></colgroup>
	<colgroup width="152"></colgroup>
	<colgroup width="85"></colgroup>
	<colgroup span="2" width="114"></colgroup>
	<colgroup width="103"></colgroup>
	<tr>
		<td  class="td-tblr2" height="21" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#26438;&#20301;</font></b></td>
		<td  class="td-tblr2" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#26438;&#20301;&#29366;&#24577;</font></b></td>
		<td  class="td-tblr2" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#21462;&#30005;&#20301;&#32622;</font></b></td>
		<td  class="td-tblr2" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#21462;&#30005;&#25910;&#36153;&#26041;&#24335;</font></b></td>
		<td  class="td-tblr2" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#25509;&#20837;&#36816;&#33829;&#21830;</font></b></td>
		<td  class="td-tblr2" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#25152;&#23646;&#20998;&#32452;</font></b></td>
		<td  class="td-tblr2" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#25152;&#23646;&#21306;&#22495;</font></b></td>
		<td  class="td-tblr2" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#32463;&#24230;</font></b></td>
		<td  class="td-tblr2" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#32428;&#24230;</font></b></td>
		<td  class="td-tblr2" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#32463;&#32428;&#24230;&#35268;&#33539;</font></b></td>
	</tr>
  #for(x : omAsset.queryAll(mixQuery.asset??).content)
	<tr>
		<td  class="td-tblr1" height="21" align="left" valign=middle><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['0']??)</font></td>
		<td  class="td-tblr1" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['1']??)</font></td>
		<td  class="td-tblr1" align="left" valign=top><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['2']??)</font></td>
		<td  class="td-tblr1" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['3']??)</font></td>
		<td  class="td-tblr1" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['4']??)</font></td>
		<td  class="td-tblr1" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['5']??)</font></td>
		<td  class="td-tblr1" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['6']??)</font></td>
		<td  class="td-tblr1" align="left" valign=middle bgcolor="#CCFFFF" sdval="116.77428102702" sdnum="1033;"><font face="&#23435;&#20307;" size=2 color="#000000">#(x.extend.data['7']??)</font></td>
		<td  class="td-tblr1" align="left" valign=middle bgcolor="#CCFFFF" sdval="40.033515259204" sdnum="1033;"><font face="&#23435;&#20307;" size=2 color="#000000">#(x.extend.data['8']??)</font></td>
		<td  class="td-tblr1" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['9']??)</font></td>
	</tr>
  #end
</table>
<!-- ************************************************************************** -->
</body>

</html>
