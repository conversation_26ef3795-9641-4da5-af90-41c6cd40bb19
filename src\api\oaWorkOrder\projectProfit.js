import request from '@/utils/request'
import qs from 'qs';

export function income(data) {
  // const query = qs.stringify(data, { indices: false });
  return request({
    url: `api/v1/excel/convert/income`,
    method: 'post',
    data
  })
}

export function cost(data) {
  const query = qs.stringify(data, { indices: false });
  return request({
    url: `api/v1/excel/convert/cost?${query}`,
    method: 'post',
    data
  })
}

export default {
  income,
  cost
}
