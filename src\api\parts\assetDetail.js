import request from '@/utils/request'
// import qs from 'qs'
// 资产管理
export function get(params) {
  return request({
    url: 'api/omAssetDetail',
    method: 'get',
    params
  })
}

export function getOmAssetSmall(params) {
  return request({
    url: 'api/omAssetDetail/small',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/omAssetDetail',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/omAssetDetail',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/omAssetDetail',
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: 'api/omAssetDetail/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'api/omAssetDetail/update/relation',
    method: 'put',
    data
  })
}

export function updateAssetId(data) {
  return request({
    url: 'api/omAssetDetail/update/assetId',
    method: 'put',
    data
  })
}

export default { getOmAssetSmall, get, add, edit, del, updateFormStruct, updateRelation, updateAssetId }
