import request from '@/utils/request'
// import qs from 'qs'
// 绑定规则
export function get(params) {
  return request({
    url: 'api/extendMenuRule',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/extendMenuRule',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/extendMenuRule',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/extendMenuRule',
    method: 'put',
    data
  })
}

export default { get, add, edit, del }
