<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">

<html>
<head>
	<meta charset="UTF-8">
	<meta http-equiv="content-type" content="text/html; charset=iso-8859-1"/>
	<title>别名信息</title>
	<meta name="generator" content="LibreOffice 5.4.7.2 (Linux)"/>
	<meta name="author" content="zhangdanqing"/>
	<meta name="created" content="2022-08-26T15:09:00"/>
	<meta name="changedby" content="admin"/>
	<meta name="changed" content="2022-09-29T02:50:03"/>
	<meta name="AppVersion" content="16.0300"/>
	<meta name="DocSecurity" content="0"/>
	<meta name="HyperlinksChanged" content="false"/>
	<meta name="ICV" content="67222D90FDE14E5285CC1111CD235FDE"/>
	<meta name="KSOProductBuildVer" content="2052-11.1.0.12358"/>
	<meta name="LinksUpToDate" content="false"/>
	<meta name="ScaleCrop" content="false"/>
	<meta name="ShareDoc" content="false"/>
	<style type="text/css">
		body,div,table,thead,tbody,tfoot,tr,th,td,p { font-family:"&#23435;&#20307;"; font-size:small }
		a.comment-indicator:hover + comment { background:#ffd; position:absolute; display:block; border:1px solid black; padding:0.5em;  } 
		a.comment-indicator { background:red; display:inline-block; border:1px solid black; width:0.5em; height:0.5em;  } 
		comment { display:none;  } 
    .td-tblr {
      border-top: 1px solid #000000;
      border-bottom: 1px solid #000000;
      border-left: 1px solid #000000;
      border-right: 1px solid #000000;
    }

    .td-blr {
      border-bottom: 1px solid #000000;
      border-left: 1px solid #000000;
      border-right: 1px solid #000000
    }

    .td-bl {
      border-bottom: 1px solid #000000;
      border-left: 1px solid #000000;
    }
	</style>
	
</head>

<body>
<table cellspacing="0" border="0" style='border-collapse: 
collapse;table-layout:fixed;min-width:480pt'>
	<colgroup width="89"></colgroup>
	<colgroup width="241"></colgroup>
	<colgroup width="255"></colgroup>
	<tr>
		<td class="td-tblr" height="19" align="center" valign=middle bgcolor="#BFBFBF"><b><font face="DejaVu Sans Mono" size=2>&#24207;&#21495;</font></b></td>
		<td class="td-tblr" align="center" valign=middle bgcolor="#BFBFBF"><b><font face="DejaVu Sans Mono" size=2>&#36335;&#21475;&#21517;&#31216;</font></b></td>
		<td class="td-tblr" align="center" valign=middle bgcolor="#BFBFBF"><b><font face="DejaVu Sans Mono" size=2>&#21035;&#21517;</font></b></td>
	</tr>
  #for(x : omAlias.queryAll(mixQuery.alias??))
	<tr>
		<td class="td-tblr" height="19" align="center" valign=middle bgcolor="#FFFFFF" sdval="1" sdnum="1033;"><font size=2 color="#000000">#(for.count)</font></td>
		<td class="td-tblr" align="center" valign=middle bgcolor="#FFFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.title??)</font></td>
		<td class="td-tblr" align="center" valign=middle bgcolor="#FFFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.description??)</font></td>
	</tr>
  #end
</table>
<!-- ************************************************************************** -->
</body>

</html>
