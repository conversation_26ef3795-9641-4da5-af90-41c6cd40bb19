@import 'variables';
@import 'mixin';
@import 'transition';
@import 'element-ui';
@import 'sidebar';
@import 'btn';
@import 'eladmin';
@import 'vxeTable';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0 !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px 20px 45px 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

//点聚合样式
.clusterBubble {
  border-radius: 50%;
  color: #fff;
  font-weight: 500;
  text-align: center;
  opacity: 0.88;
  background-image: linear-gradient(139deg, #4294FF 0%, #295BFF 100%);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.20);
  position: absolute;
  top: 0px;
  left: 0px;
}

//地图弹窗样式
//地图弹窗样式
.infowindowBtn {
  margin-top: 10px;
}

.xunjian, .details, .edit, .disabled {
  border-radius: 5px;
  padding: 5px;
  color: #ffffff;
  border: none;
  margin: 0 3px;
  cursor: pointer;
}

.xunjian {
  background: linear-gradient(180deg, #43db6e);
}

.details {
  background: linear-gradient(180deg, #43db6e);
}

.edit {
  background: linear-gradient(180deg, #43db6e);
}

.disabled {
  background: #89e7b3;
  color: #fff;
}

// 地图上雪亮资产弹窗样式
.xlAsset-info-item {
  display: flex;
  align-items: center;

}

.xlAsset-box-oper {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.xlAsset-box-btn {
  border-radius: 5px;
  padding: 5px;
  color: #ffffff;
  border: none;
  margin: 0 3px;
  cursor: pointer;
  background: linear-gradient(180deg, #43db6e);
}
