import request from '@/utils/request'
import qs from 'qs'

export function get(params) {
  return request({
    url: 'api/oaPmMilestone' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getSmall(params) {
  return request({
    url: 'api/oaPmMilestone' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: 'api/oaPmMilestone',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/oaPmMilestone',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/oaPmMilestone',
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: 'api/oaPmMilestone/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'api/oaPmMilestone/update/relation',
    method: 'put',
    data
  })
}

export default { get, getSmall, add, edit, del, updateFormStruct, updateRelation }
