$primary-color: #409EFF;
$primary-background-color: #ecf5ff;

*, :after, :before {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.fa-icon{
  width: auto;
  height: 1em; /* 或任意其它字体大小相对值 */

  /* 要在 Safari 中正常工作，需要再引入如下两行代码 */
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
}

.fm2-container{
  background: #fff;
  height: 100%;
  border: 1px solid #e0e0e0;

  .el-container{
    height: 100% !important;
  }

  &>.el-container{
    background: #fff;
  }
  .fm2-main{
    position: relative;

    &>.el-container{
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
    }
  }
  main{
    padding: 0;
  }

  footer{
    height: 30px;
    line-height: 30px;
    border-top: 1px solid #e0e0e0;
    font-size: 12px;
    text-align: right;
    color: $primary-color;
    background: #fafafa;

    a{
      color: $primary-color;
    }
  }
}

.center-container{
  border-left: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;

  .btn-bar{
    height: 45px;
    line-height: 45px;
    font-size: 18px;
    border-bottom: solid 2px #e4e7ed;
    text-align: right;
  }

  .el-main{
    padding: 0;
    position: relative;
    background: #fafafa;
  }
}

.components-list{
  padding: 8px 0;
  width: 100%;
  height: 100%;

  .widget-cate{
    padding: 8px 12px;
    font-size: 13px;
  }

  ul{
    position: relative;
    overflow: hidden;
    padding: 0 10px 10px;
    margin: 0;
  }

  .form-edit-widget-label{
    font-size: 12px;
    display: block;
    width: 48%;
    line-height: 26px;
    position: relative;
    float: left;
    left: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 1%;
    color: #333;
    border: 1px solid #F4F6FC;

    &:hover{
      color: $primary-color;
      border: 1px dashed $primary-color;
    }

    &>a{
      display: block;
      cursor: move;
      background: #F4F6FC;
      border: 1px solid #F4F6FC;

      .icon{
        margin-right: 6px;
        margin-left: 8px;
        font-size: 14px;
        display: inline-block;
        vertical-align: middle;
      }

      span{
        display: inline-block;
        vertical-align: middle;
      }
    }
  }
}

.widget-form-container{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  

  .widget-form-list{
    background: #fff;
    // border: 1px dashed #999;
    border: 1px dashed #999;
    min-height: 600px;
    margin: 10px;

    .widget-col-list{
      min-height: 50px;
      border: 1px dashed #ccc;
      background: #fff;
    }

    .widget-view{
      padding-bottom: 18px;
      position: relative;
      border: 1px dashed rgba(170,170,170,0.7);
      background-color: rgba(236, 245, 255, .3);
      margin: 2px;

      .el-form-item__content{
        position: unset;
      }

      &.is_req{
        .el-form-item__label::before{
          content: '*';
          color: #f56c6c;
          margin-right: 4px;
        }
      }

      .widget-view-description{
        height: 15px;
        line-height: 15px;
        font-size:13px;
        margin-top: 6px;
        color:#909399;
      }

      .widget-view-action{
        position: absolute;
        right: 0;
        bottom: 0;
        height: 28px;
        line-height: 28px;
        background: $primary-color;
        z-index: 9;

        i{
          font-size: 14px;
          color: #fff;
          margin: 0 5px;
          cursor: pointer;
          
        }
      }

      .widget-view-drag{
        position: absolute;
        left: -2px;
        top: -2px;
        bottom: -18px;
        height: 28px;
        line-height: 28px;
        background: $primary-color;
        z-index: 9;
        // display: none;

        i{
          font-size: 14px;
          color: #fff;
          margin: 0 5px;
          cursor: move;
        }
      }

      &:after{
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        display: block;
      }

      &:hover{
        background: $primary-background-color;
        outline: 1px solid $primary-color;
        outline-offset: 0px;

        &.active{
          outline: 2px solid $primary-color;
          border: 1px solid $primary-color;
          outline-offset: 0;
        }

        .widget-view-drag{
          display: block;
        }
      }

      &.active{
        outline: 2px solid $primary-color;
        border: 1px solid $primary-color;
      }

      &.ghost{
        background: #F56C6C;
        border: 2px solid #F56C6C;
        outline-width: 0;
        height: 3px;
        box-sizing: border-box;
        font-size: 0;
        content: '';
        overflow: hidden;
        padding: 0;
      }
    }

    .widget-table{
      padding-bottom: 0;
      padding: 5px;
      background-color: rgba(253,246,236, .3);

      .widget-table-wrapper{
        min-height: 50px;
        background: #fff;
        display: flex;
        justify-content: flex-start;

        .widget-table-row{
          td{
            border-bottom: 0;
          }
        }

        .widget-table-left{
          width: 51px;
          border-left: 1px solid #EBEEF5;
          border-right: 1px solid #EBEEF5;
          border-top: 1px solid #EBEEF5;
          flex: none;
        }

        .widget-table-view{
          border: 1px solid #EBEEF5;
          width: 200px;
          float: left;
          height: 100%;
          position: relative;
          display: block;

          .el-table{
            height: 100%;
          }

          &.is_req{
            .el-form-item__label::before{
              content: '*';
              color: #f56c6c;
              margin-right: 4px;
            }
          }
    
          .widget-view-description{
            height: 15px;
            line-height: 15px;
            font-size:13px;
            margin-top: 6px;
            color:#909399;
          }
    
          .widget-view-action{
            position: absolute;
            right: 0;
            bottom: 0;
            height: 28px;
            line-height: 28px;
            background: $primary-color;
            z-index: 9;
    
            i{
              font-size: 14px;
              color: #fff;
              margin: 0 5px;
              cursor: pointer;
              
            }
          }
    
          .widget-view-drag{
            position: absolute;
            left: -2px;
            top: -2px;
            bottom: -18px;
            height: 28px;
            line-height: 28px;
            background: $primary-color;
            z-index: 9;
            // display: none;
    
            i{
              font-size: 14px;
              color: #fff;
              margin: 0 5px;
              cursor: move;
            }
          }
    
          &::after{
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            display: block;
            content: '';
          }

          &::before{
            display: none;
          }
    
          &:hover{
            background: $primary-background-color;
            outline: 1px solid $primary-color;
            outline-offset: -1px;
    
            &.active{
              // outline: 1px solid $primary-color;
              border: 1px solid $primary-color;
              outline: 1px solid $primary-color;
              outline-offset: -1px;
            }
    
            .widget-view-drag{
              display: block;
            }
          }
    
          &.active{
            outline: 1px solid $primary-color;
            border: 1px solid $primary-color;
            outline-offset: -1px;
          }
    
          &.ghost{
            background: #F56C6C;
            outline-width: 0;
            width: 5px !important;
            box-sizing: border-box;
            font-size: 0;
            content: '';
            overflow: hidden;
            padding: 0;
            position: relative;
            outline: none !important;
            border: 0 !important;

            &::after{
              background: #F56C6C;
              position: absolute;
              top: 0;
              left: 0;
              bottom: 0;
              right: 0;
              z-index: 9999;
              content: '';
              outline: none;
            }
          }
        }

        .widget-table-content{
          width:100%;
          // border: 1px dashed #ccc;
          outline: 1px dashed #ccc;
          background: #fff;
          flex:1;
          margin: 0 1px;
          overflow: auto;

          &>div{
            height: 100%;
          }

          .widget-table-col{
            height: 100%;
            
            .ghost{
              background: #F56C6C;
              // border: 2px solid #F56C6C;
              position: relative;
              content: '';
              float: left;
              height: 100%;
              width: 5px !important;
              list-style: none;
              font-size: 0;
              overflow: hidden;
              outline: none;
            
              &::after{
                background: #F56C6C;
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                z-index: 9999;
                content: '';
                outline: none;
              }
            }
          }
        }
      }

      &.active{
        outline: 2px solid #e6a23c;
        border: 1px solid #e6a23c;
      }

      &:hover{
        background: #fdf6ec;
        outline: 1px solid #e6a23c;
        outline-offset: 0px;

        &.active{
          outline: 2px solid #e6a23c;
          border: 1px solid #e6a23c;
          outline-offset: 0;
        }
      }

      .widget-view-action.widget-col-action{
        background: #e6a23c;
      }

      .widget-view-drag.widget-col-drag{
        background: #e6a23c;
      }

      &::after{
        display: none;
      }

      &.ghost{
        background: #F56C6C;
        outline-width: 0;
        height: 5px;
        box-sizing: border-box;
        font-size: 0;
        content: '';
        overflow: hidden;
        padding: 0;
        position: relative;
        outline: none;
        border: 0;

        &::after{
          background: #F56C6C;
          position: absolute;
          top:0;
          left: 0;
          right: 0;
          bottom: 0;
          content: '';
          display: block;
          z-index: 999;
        }
      }
    }

    .widget-col{
      padding-bottom: 0;
      padding: 5px;
      // margin-left: 2px !important;
      // margin-right: 2px !important;
      background-color: rgba(253,246,236, .3);

      &.active{
        outline: 2px solid #e6a23c;
        border: 1px solid #e6a23c;
      }

      &:hover{
        background: #fdf6ec;
        outline: 1px solid #e6a23c;
        outline-offset: 0px;

        &.active{
          outline: 2px solid #e6a23c;
          border: 1px solid #e6a23c;
          outline-offset: 0;
        }
      }

      .el-col{
        min-height: 50px;
      }

      &.ghost{
        background: #F56C6C;
        border: 2px solid #F56C6C;
        outline-width: 0;
        height: 3px;
        box-sizing: border-box;
        font-size: 0;
        content: '';
        overflow: hidden;
        padding: 0;
      }

      .widget-view-action.widget-col-action{
        background: #e6a23c;
      }

      .widget-view-drag.widget-col-drag{
        background: #e6a23c;
      }

      &::after{
        display: none;
      }
    }

    .ghost{
      background: #F56C6C;
      border: 2px solid #F56C6C;
      outline-width: 0;
      height: 3px;
      box-sizing: border-box;
      font-size: 0;
      content: '';
      overflow: hidden;
      padding: 0;
    }
  }

  .ghost{
    background: #F56C6C;
    border: 2px solid #F56C6C;
    position: relative;
  
    &::after{
      background: #F56C6C;
    }
  }
  
  li.ghost{
    height: 5px;
    list-style: none;
    font-size: 0;
    overflow: hidden;
  }

  .widget-grid{
    background: #F4F6FC;
    position: relative;
    border-left: 5px solid transparent;
    padding: 5px;
    margin: 0 !important;

    &.active{
      border-left: 5px solid $primary-color;
      background: #b3d8ff;
    }
  }

  .widget-grid-container{
    &.ghost{
      background: #F56C6C;
      border: 2px solid #F56C6C;
      outline-width: 0;
      height: 3px;
      box-sizing: border-box;
      font-size: 0;
      content: '';
      overflow: hidden;
      padding: 0;
    }
  }

  .ghost{
    background: #F56C6C;
    border: 2px solid #F56C6C;
    position: relative;
  
    &::after{
      background: #F56C6C;
    }
  }
  
  li.ghost{
    height: 5px;
    list-style: none;
    font-size: 0;
    overflow: hidden;
  }
}

.widget-config-container{
  position: relative;

  .el-header{
    border-bottom: solid 2px #e4e7ed;
    padding: 0 5px;
  }

  .config-tab{
    height: 45px;
    line-height: 45px;
    display: inline-block;
    width: 143px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    position: relative;
    cursor: pointer;

    &.active{
      border-bottom: solid 2px $primary-color;
    }
  }

  .config-content{
    padding: 10px;

    .el-form-item__label{
      padding: 0;
      font-weight: 500;
    }

    .el-form-item {
      border-bottom: solid 1px #e1e1e1;
      padding-bottom: 10px;
    }
  }

  .ghost{
    background: #fff;
    border: 1px dashed $primary-color;
  
    &::after{
      background: #fff;
      display: block;
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
  
  ul{
    margin: 0;
    padding: 0;
  }
  
  li.ghost{
    list-style: none;
    font-size: 0;
    display: block;
    position: relative;
  }
}

.viewer-container{
  z-index: 99999 !important;
}

.form-empty{
  position: absolute;
  text-align: center;
  width: 300px;
  font-size: 20px;
  top: 200px;
  left: 50%;
  margin-left: -150px;
  color: #ccc;
}
