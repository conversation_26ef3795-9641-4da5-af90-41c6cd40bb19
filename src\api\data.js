import request from '@/utils/request'
import qs from 'qs'

export function initData(url, params, config = {}) {
  const { method } = config
  if (method == 'post') {
    const { size, sort, page } = params
    const pageParams = {
      size: size,
      sort: sort,
      page: page
    }
    return request({
      url: url + '?' + qs.stringify(pageParams, { indices: false }),
      method: 'post',
      data: params
    })
  } else {
    return request({
      url: url + '?' + qs.stringify(params, { indices: false }),
      method: 'get',
      ...config
    })
  }
}

export function download(url, params) {
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get',
    responseType: 'blob'
  })
}
