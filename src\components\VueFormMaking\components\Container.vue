<template>
  <el-container class="fm2-container">
    <el-main class="fm2-main">
      <el-container>
        <!--左侧区域-->
        <el-aside width="250px">
          <div class="components-list">
            <template v-if="basicFields.length">
              <div class="widget-cate">{{ $t('fm.components.basic.title') }}</div>
              <draggable
                :list="basicComponents"
                :move="handleMove"
                tag="ul"
                v-bind="{group:{ name:'people', pull:'clone',put:false},sort:false, ghostClass: 'ghost'}"
                @end="handleMoveEnd"
                @start="handleMoveStart"
              >
                <template v-for="(item, index) in basicComponents">
                  <li
                    v-if="basicFields.indexOf(item.type)>=0"
                    :key="index"
                    :class="{'no-put': item.type == 'divider'}"
                    class="form-edit-widget-label"
                  >
                    <a>
                      <i :class="item.icon" class="icon iconfont" />
                      <span>{{ item.name }}</span>
                    </a>
                  </li>
                </template>

              </draggable>
            </template>
            <!--高级字段-->
            <template v-if="advanceFields.length">
              <div class="widget-cate">{{ $t('fm.components.advance.title') }}</div>
              <draggable
                :list="advanceComponents"
                :move="handleMove"
                tag="ul"
                v-bind="{group:{ name:'people', pull:'clone',put:false},sort:false, ghostClass: 'ghost'}"
                @end="handleMoveEnd"
                @start="handleMoveStart"
              >
                <template v-for="(item, index) in advanceComponents">
                  <li
                    v-if="advanceFields.indexOf(item.type) >= 0"
                    :key="index"
                    :class="{'no-put': item.type == 'table'}"
                    class="form-edit-widget-label"
                  >
                    <a>
                      <i :class="item.icon" class="icon iconfont" />
                      <span>{{ item.name }}</span>
                    </a>
                  </li>
                </template>

              </draggable>
            </template>
            <!--布局字段-->
            <template v-if="layoutFields.length">
              <div class="widget-cate">{{ $t('fm.components.layout.title') }}</div>
              <draggable
                :list="layoutComponents"
                :move="handleMove"
                tag="ul"
                v-bind="{group:{ name:'people', pull:'clone',put:false},sort:false, ghostClass: 'ghost'}"
                @end="handleMoveEnd"
                @start="handleMoveStart"
              >
                <template v-for="(item, index) in layoutComponents">
                  <li v-if="layoutFields.indexOf(item.type) >=0" :key="index" class="form-edit-widget-label no-put">
                    <a>
                      <i :class="item.icon" class="icon iconfont" />
                      <span>{{ item.name }}</span>
                    </a>
                  </li>
                </template>

              </draggable>
            </template>

          </div>

        </el-aside>
        <!--中间主要内容-->
        <el-container class="center-container" direction="vertical">
          <!--导入json、清空、预览、生成json、生成代码-->
          <el-header class="btn-bar" style="height: 45px;">
            <slot name="action" />
            <el-button v-if="upload" icon="el-icon-upload2" size="medium" type="text" @click="handleUpload">
              {{ $t('fm.actions.import') }}
            </el-button>
            <el-button v-if="clearable" icon="el-icon-delete" size="medium" type="text" @click="handleClear">
              {{ $t('fm.actions.clear') }}
            </el-button>
            <el-button v-if="preview" icon="el-icon-view" size="medium" type="text" @click="handlePreview">
              {{ $t('fm.actions.preview') }}
            </el-button>
            <el-button
              v-if="generateJson"
              icon="el-icon-tickets"
              size="medium"
              type="text"
              @click="handleGenerateJson"
            >{{ $t('fm.actions.json') }}
            </el-button>
            <el-button
              v-if="generateCode"
              icon="el-icon-document"
              size="medium"
              type="text"
              @click="handleGenerateCode"
            >{{ $t('fm.actions.code') }}
            </el-button>
          </el-header>
          <!--中间区域-->
          <el-main :class="{'widget-empty': widgetForm.list.length == 0}">
            <widget-form v-if="!resetJson" ref="widgetForm" :data="widgetForm" :select.sync="widgetFormSelect" />
          </el-main>
        </el-container>
        <!--右侧配置-->
        <el-aside class="widget-config-container" style="width: 305px;">
          <el-container>
            <el-header height="45px">
              <div :class="{active: configTab=='widget'}" class="config-tab" @click="handleConfigSelect('widget')">
                {{ $t('fm.config.widget.title') }}
              </div>
              <div :class="{active: configTab=='form'}" class="config-tab" @click="handleConfigSelect('form')">
                {{ $t('fm.config.form.title') }}
              </div>
            </el-header>
            <el-main class="config-content">
              <!--字段配置信息-->
              <widget-config v-if="widgetFormSelect!==null" v-show="configTab=='widget'" :data="widgetFormSelect" />
              <!--表单整体配置信息-->
              <form-config v-show="configTab=='form'" :data="widgetForm.config" />
            </el-main>
          </el-container>

        </el-aside>
        <!--表单预览-->
        <cus-dialog
          ref="widgetPreview"
          :visible="previewVisible"
          form
          width="1000px"
          @on-close="previewVisible = false"
        >
          <generate-form
            v-if="previewVisible"
            ref="generateForm"
            :data="widgetForm"
            :remote="remoteFuncs"
            :value="widgetModels"
            insite="true"
            @on-change="handleDataChange"
          >

            <template slot-scope="scope">
              Width
              <el-input v-model="scope.model.blank.width" style="width: 100px" />
              Height
              <el-input v-model="scope.model.blank.height" style="width: 100px" />
            </template>
          </generate-form>

          <template slot="action">
            <el-button type="primary" @click="handleTest">{{ $t('fm.actions.getData') }}</el-button>
            <el-button @click="handleReset">{{ $t('fm.actions.reset') }}</el-button>
          </template>
        </cus-dialog>
        <!--导入Json弹框-->
        <cus-dialog
          ref="uploadJson"
          :visible="uploadVisible"
          form
          width="800px"
          @on-close="uploadVisible = false"
          @on-submit="handleUploadJson"
        >
          <el-alert :title="$t('fm.description.uploadJsonInfo')" type="info" />
          <div id="uploadeditor" style="height: 400px;width: 100%;">{{ jsonEg }}</div>
        </cus-dialog>
        <!--生成json预览-->
        <cus-dialog
          ref="jsonPreview"
          :visible="jsonVisible"
          form
          width="800px"
          @on-close="jsonVisible = false"
        >
          <div id="jsoneditor" style="height: 400px;width: 100%;">{{ jsonTemplate }}</div>

          <template slot="action">
            <el-button :data-clipboard-text="jsonCopyValue" class="json-btn" type="primary">
              {{ $t('fm.actions.copyData') }}
            </el-button>
          </template>
        </cus-dialog>
        <!--生成代码-->
        <cus-dialog
          ref="codePreview"
          :action="false"
          :visible="codeVisible"
          form
          width="800px"
          @on-close="codeVisible = false"
        >
          <div id="codeeditor" style="height: 500px; width: 100%;">{{ htmlTemplate }}</div>
        </cus-dialog>
      </el-container>
    </el-main>
    <!-- <el-footer height="30px" style="font-weight: 600;">Powered by <a target="_blank" href="http://www.fdevops.com">fdevops</a></el-footer> -->
  </el-container>

</template>

<script>
import Draggable from 'vuedraggable'
import WidgetConfig from './WidgetConfig'
import FormConfig from './FormConfig'
import WidgetForm from './WidgetForm'
import CusDialog from './CusDialog'
import GenerateForm from './GenerateForm'
import Clipboard from 'clipboard'
import { basicComponents, layoutComponents, advanceComponents } from './componentsConfig.js'
// import request from '../util/request.js'
import generateCode from './generateCode.js'
import { getToken } from '@/utils/auth'

var ace = require('ace-builds/src-noconflict/ace')
ace.config.set('basePath', '/lib/ace')
ace.config.set('modePath', '/lib/ace')
ace.config.set('themePath', '/lib/ace')
window.define = window.define || ace.define
window.require = window.require || ace.require

export default {
  name: 'FmMakingForm',
  components: {
    Draggable,
    WidgetConfig,
    FormConfig,
    WidgetForm,
    CusDialog,
    GenerateForm
  },
  props: {
    preview: {
      type: Boolean,
      default: false
    },
    generateCode: {
      type: Boolean,
      default: false
    },
    generateJson: {
      type: Boolean,
      default: false
    },
    upload: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    basicFields: {
      type: Array,
      default: () => ['input', 'textarea', 'number', 'radio', 'checkbox', 'time', 'date', 'rate', 'color', 'select', 'switch', 'slider', 'text', 'auxiliary']
    },
    advanceFields: {
      type: Array,
      default: () => ['blank', 'imgupload', 'editor', 'cascader', 'treeSelect']
    },
    layoutFields: {
      type: Array,
      default: () => ['grid', 'divider']
    }
  },
  data() {
    return {
      basicComponents,
      layoutComponents,
      advanceComponents,
      resetJson: false,
      widgetForm: {
        list: [],
        config: {
          labelWidth: 100,
          labelPosition: 'right',
          size: 'small',
          name: '' // 为了区分表单做特殊处理
        }
      },
      configTab: 'widget',
      widgetFormSelect: null,
      previewVisible: false,
      jsonVisible: false,
      codeVisible: false,
      uploadVisible: false,
      remoteFuncs: {
        func_test(resolve) {
          setTimeout(() => {
            const options = [
              { id: '1', name: '1111' },
              { id: '2', name: '2222' },
              { id: '3', name: '3333' }
            ]

            resolve(options)
          }, 2000)
        },
        funcGetToken(resolve) {
          resolve(getToken())
          // request.get('http://tools-server.xiaoyaoji.cn/api/uptoken').then(res => {
          //   resolve(res.uptoken)
          // })
        },
        upload_callback(response, file, fileList) {
        }
      },
      widgetModels: {},
      blank: '',
      htmlTemplate: '',
      jsonTemplate: '',
      uploadEditor: null,
      jsonCopyValue: '',
      jsonClipboard: null,
      jsonEg: `{
  "list": [],
  "config": {
    "labelWidth": 100,
    "labelPosition": "top",
    "size": "small"
  }
}`
    }
  },
  watch: {
    widgetForm: {
      deep: true,
      handler: function(val) {
      }
    },
    '$lang': function(val) {
      this._loadComponents()
    }
  },
  mounted() {
    this._loadComponents()
  },
  methods: {
    _loadComponents() {
      this.basicComponents = this.basicComponents.map(item => {
        return {
          ...item,
          name: this.$t(`fm.components.fields.${item.type}`)
        }
      })
      this.advanceComponents = this.advanceComponents.map(item => {
        return {
          ...item,
          name: this.$t(`fm.components.fields.${item.type}`)
        }
      })
      console.log('this.advanceComponents<===>', this.advanceComponents)
      this.layoutComponents = this.layoutComponents.map(item => {
        return {
          ...item,
          name: this.$t(`fm.components.fields.${item.type}`)
        }
      })
    },
    handleConfigSelect(value) {
      this.configTab = value
    },
    handleMoveEnd(evt) {
    },
    handleMoveStart({ oldIndex }) {
    },
    handleMove() {
      return true
    },
    handlePreview() {
      this.previewVisible = true
    },
    handleTest() {
      this.$refs.generateForm.getData().then(data => {
        this.$alert(data, '').catch(e => {
        })
        this.$refs.widgetPreview.end()
      }).catch(e => {
        this.$refs.widgetPreview.end()
      })
    },
    handleReset() {
      this.$refs.generateForm.reset()
    },
    handleGenerateJson() {
      this.jsonVisible = true
      this.jsonTemplate = this.widgetForm
      this.$nextTick(() => {
        const editor = ace.edit('jsoneditor')
        editor.session.setMode('ace/mode/json')

        if (!this.jsonClipboard) {
          this.jsonClipboard = new Clipboard('.json-btn')
          this.jsonClipboard.on('success', (e) => {
            this.$message.success(this.$t('fm.message.copySuccess'))
          })
        }
        this.jsonCopyValue = JSON.stringify(this.widgetForm)
      })
    },
    handleGenerateCode() {
      this.codeVisible = true
      this.htmlTemplate = generateCode(JSON.stringify(this.widgetForm))
      this.$nextTick(() => {
        const editor = ace.edit('codeeditor')
        editor.session.setMode('ace/mode/html')
      })
    },
    handleUpload() {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.uploadEditor = ace.edit('uploadeditor')
        this.uploadEditor.session.setMode('ace/mode/json')
      })
    },

    handleUploadJson() {
      try {
        this.setJSON(JSON.parse(this.uploadEditor.getValue()))
        this.uploadVisible = false
      } catch (e) {
        this.$message.error(e.message)
        this.$refs.uploadJson.end()
      }
    },
    handleClear() {
      this.widgetForm = {
        list: [],
        config: {
          labelWidth: 100,
          labelPosition: 'right',
          size: 'small',
          customClass: ''
        }
      }

      this.widgetFormSelect = {}
    },
    getJSON() {
      return this.widgetForm
    },
    getHtml() {
      return generateCode(JSON.stringify(this.widgetForm))
    },
    setJSON(json) {
      this.widgetForm = json
      if (json.list.length > 0) {
        this.widgetFormSelect = json.list[0]
      }
    },
    handleInput(val) {
      this.blank = val
    },
    handleDataChange(field, value, data) {
      console.log(888, '<===>', '888')
      this.$emit('dataChange', field, value, data)
    }
  }
}
</script>

<style lang="scss">
.widget-empty {
	background-position: 50%;
}

aside {
	background: #ffffff;
	padding: 0;
	margin-bottom: 0;

	a {
		color: #333333;
	}

}
</style>
