<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>遮挡上报</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>

<script>

</script>

<table>
    <thead>
    <tr>
        <th>编号</th>
        <th>路口名称</th>
        <th>杆体编号</th>
        <th>位置描述</th>
        <th>遮挡照片</th>
        <th>状态</th>
        <th>描述</th>
        <th>上传人</th>
        <th>上传日期</th>
    </tr>
    </thead>
    <tbody>
    #for(x : omAssetAffiliated.queryAll(mixQuery.affiliated??,pageable??).content)
    <tr>
        <td>#(x.fv4??)</td>
        <td>#(x.title??)</td>
        <td>#(x.fv5??)</td>
        <td>#(x.fv1??)</td>
        <td height="260" width="30%">
            #if(x.ft1??)
            #for(y : jsonToList(x.ft1)??)
            <img alt="" height="260" src="#(y.thUrl)?x-oss-process=image/resize,h_200,m_lfit" style="margin: 5px;"
                 width="200"/>
            #end
            #end
        </td>
        <td>#(x.fv9??)</td>
        <td>#(x.ft2??)</td>
        <td>#(x.createBy??)</td>
        <td>#(x.createTime??)</td>
    </tr>
    #end
    </tbody>
</table>

</body>
</html>
