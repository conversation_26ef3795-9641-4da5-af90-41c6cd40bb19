<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>路口数据表</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
#set(omAsset = omAsset.queryAllSmallWithAutoPilot(mixQuery.asset??,pageable??))
<table>
    <thead>
    <tr>
        <th>路口编号</th>
        <th>路口名称</th>
        #for(x : omAsset.tableHeader)
        <th>#(x.label??)</th>
        #end
    </tr>
    </thead>
    <tbody>
    #for(x : omAsset.content)
    <tr>
        <td>#(x.fv4??)</td>
        <td>#(x.title??)</td>
        #for(z : omAsset.tableHeader)
        <td>#(x.tagMap[z.label]??)</td>
        #end
    </tr>
    #end
    </tbody>
</table>
</body>
</html>
