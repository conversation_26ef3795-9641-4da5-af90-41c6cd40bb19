import request from '@/utils/request'
import qs from 'qs'

export function getAllPmTree(params) {
  const url = 'api/oaPmTree/all';
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function addToMemberAndAuth(params) {
  const url = 'api/oaPmTree/addToMemberAndAuth';
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

// 获取所有树  树形解构
export function getAllPmTreeLists(params) {
  console.log(params, '<===>', 'params')
  const url = 'api/oaPmTree/all/tree';
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getPmTree(params) {
  const url = 'api/oaPmTree';
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getPmTreeSmall(params) {
  const url = 'api/oaPmTree/small';
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getPmTreeSuperior(params) {
  const url = 'api/oaPmTree/superior'
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function oneLevelSubproject(params) {
  const url = 'api/oaPmTree/oneLevelSubproject'
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getCountGroupByPmName(params) {
  const url = 'api/oaPmTree/countGroupByPmName'
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getPmTreeParent(params) {
  const url = 'api/oaPmTree/parent'
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: 'api/oaPmTree',
    method: 'post',
    data
  })
}

export function createTask(data) {
  return request({
    url: 'api/oaPmTree/createTask',
    method: 'post',
    data
  })
}

export function transfer(data) {
  return request({
    url: 'api/oaPmTree/transfer',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/oaPmTree',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/oaPmTree',
    method: 'put',
    data
  })
}

// 获取所有子级树
export function getChildren(params) {
  const url = 'api/oaPmTree/children';
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

// 更新模板
export function updateFormStruct(data) {
  return request({
    url: '/api/oaPmTree/update/formStruct',
    method: 'put',
    data
  })
}

export function updateCateNames(data) {
  return request({
    url: 'api/oaPmTree/cateNamesToIds',
    method: 'put',
    data
  })
}

// 更新绑定关系
export function updateRelation(data) {
  return request({
    url: 'api/oaPmTree/update/relation',
    method: 'put',
    data
  })
}

export function editMember(data) {
  return request({
    url: 'api/oaPmTree/update/members',
    method: 'put',
    data
  })
}

export function createDir(data) {
  return request({
    url: 'api/oaPmTree/createDirWithTemplate',
    method: 'put',
    data
  })
}

export function updateTaskAndDirectory(params) {
  const url = 'api/oaPmTree/updateTaskAndDirectory'
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function updateFieldsByRule(params) {
  const url = 'api/oaPmTree/updateFieldsByRule'
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function addCatalogWithTemplate(params) {
  const url = 'api/oaPmTree/addCatalogWithTemplate';
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export default {
  getPmTreeSmall,
  getChildren,
  add,
  edit,
  del,
  getPmTree,
  getPmTreeSuperior,
  getPmTreeParent,
  getAllPmTreeLists,
  updateFormStruct,
  updateRelation,
  getAllPmTree,
  editMember,
  updateCateNames,
  createDir,
  createTask,
  transfer,
  addToMemberAndAuth,
  oneLevelSubproject,
  getCountGroupByPmName,
  updateTaskAndDirectory,
  updateFieldsByRule,
  addCatalogWithTemplate
}
