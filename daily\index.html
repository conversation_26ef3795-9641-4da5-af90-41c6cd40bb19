<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>巡检信息</title>
  <!-- <script src="./a/loadsh.js"></script>
  <script src="./a/jquery.js"></script>
  <script src="./a/book.js"></script> -->
  
  <!-- <style> -->
    <style type="text/css">
      table { page-break-inside:auto }
      tr    { page-break-inside:avoid; page-break-after:auto }
      thead { display:table-header-group }
      tfoot { display:table-footer-group }
    /* <p style="page-break-after: always; "></p> */
  </style>
</head>
<body>
  <div class="print-friendly" style="width:1000px;margin:0 auto; padding: 30px;page-break-after:avoid">
    <h1 style="text-align: center;"><strong>交通信号灯运维日报</strong></h1>
    <h4 style="text-align: right;">日期：#date(mixQuery.inspect.createTime[0]??, "yyyy-MM-dd") - #date(mixQuery.inspect.createTime[1]??, "yyyy-MM-dd")</h4>
    <h3>一、所有故障中的信号灯</h3>
    <table style="margin-top: 10px; border-collapse: collapse;" border="1" cellspacing="10" cellpadding="20">
      <tbody>
        <tr>
          <td width="40">
            <p>序号</p>
          </td>
          <td width="70">
            <p>上报人</p>
          </td>
          <td width="150">
            <p>故障地点</p>
          </td>
          <td width="150">
            <p>故障等级</p>
          </td>
          <td width="150">
            <p>上报时间</p>
          </td>
          <td width="150">
            <p>备注</p>
          </td>
        </tr>
        #for(x : omAsset.queryAll(mixQuery.asset??).content)
        <tr>
          <td width="40">
            <p>#(for.count)</p>
          </td>
          <td width="80">
            <p>#(x.inspect.createBy??)</p>
          </td>
          <td width="150">
            <p>#(x.title)</p>
          </td>
          <td width="150">
            <p>#(x.inspect.fv2??)</p>
          </td>
          <td width="150">
            <p>#date(x.inspect.createTime??, "yyyy-MM-dd HH:mm:ss")</p>
          </td >
          <td width="150">
            <p>#(x.inspect.fv6??)</p>
          </td>
        </tr>
        #end
      </tbody>
    </table>
    <p><strong><strong>&nbsp;</strong></strong></p>
    <p><strong><strong>&nbsp;</strong></strong></p>
    <p><strong><strong>&nbsp;</strong></strong></p>
    <p><strong><strong>&nbsp;</strong></strong></p>
    <p>附图</p>
    <ol>
      #for(x : omAsset.queryAll(mixQuery.asset??).content)
      <li value="#(for.count)">#(x.title)<br />
        #if(x.inspect.ft1??)
          #for(y : jsonToList(x.inspect.ft1)??)
            <img src="#(y.thUrl)" alt=""  width="200" height="260" />
          #end
        #end
      </li>
      #end
    </ol>
    <p><strong>&nbsp;</strong></p>
    <p><strong><strong>二、巡检记录</strong></strong></p>
    <table style="border-collapse: collapse; margin-top: 10px;" border="1" cellspacing="10" cellpadding="20">
      <tbody>
        <tr>
          <td width="40">
            <p>序号</p>
          </td>
          <td width="80">
            <p>巡检人</p>
          </td>
          <td width="150">
            <p>巡查地点</p>
          </td>
          <td width="150">
            <p>巡检结果</p>
          </td>
          <td width="200">
            <p>备注</p>
          </td>
        </tr>
        #for(p : omInspect.queryAll(mixQuery.inspect??).content)
        <tr>
          <td width="40">
            <p>#(for.count)</p>
          </td>
          <td width="40">
            <p>#(p.createBy)</p>
          </td>
          <td width="40">
            <p>#(p.asset.title)</p>
          </td>
          <td width="40">
            <p>#(p.status)</p>
          </td>
          <td width="40">
            <p>#(p.fv6??) </p>
          </td>
        </tr>
      #end
      </tbody>
    </table>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p>附图：</p>
    <ol>
      #for(p : omInspect.queryAll(mixQuery.inspect??).content)
        <li value="#(for.count)">#(p.asset.title)<br />
          #if(p.ft1??)
            #for(y : jsonToList(p.ft1)??)
              <img src="#(y.thUrl)" alt="" width="200" height="260" />
            #end
          #end
        </li>
        #end
    </ol>
    <p><strong>&nbsp;</strong></p>
    <p>&nbsp;</p>
  </div>
</body>
</html>