<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>整改台账</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>

<script>

</script>

<table>
    <thead>
    <tr>
        <th>编号</th>
        <th>路口名称</th>
        <th>检查单位</th>
        <th>检查日期</th>
        <th>整改时限</th>
        <th>区域负责人</th>
        <th>分区</th>
        <th>检查发现的问题</th>
        <th>问题照片</th>
        <th>整改后照片</th>
        <th>整改完成日期</th>
        <th>实际整改人员</th>
        <th>状态</th>
        <th>备注</th>
    </tr>
    </thead>
    <tbody>
    #for(x : omAssetAffiliated.queryAll(mixQuery.affiliated??,pageable??).content)
    <tr>
        <td>#(x.fv4??)</td>
        <td>#(x.title??)</td>
        <td>#(x.fv1??)</td>
        <td>#(x.fv2??)</td>
        <td>#(x.fv8??)</td>
        <td>#(x.fv3??)</td>
        <td>#(x.fv5??)</td>
        <td>#(x.ft3??)</td>
        <td height="260" width="30%">

            #if(x.ft1??)
            #for(y : jsonToList(x.ft1)??)
            <img alt="" height="260" src="#(y.thUrl)?x-oss-process=image/resize,h_200,m_lfit" style="margin: 5px;"
                 width="200"/>
            #end
            #end
        </td>
        <td height="260" width="30%">
            #if(x.ft2??)
            #for(y : jsonToList(x.ft2)??)
            <img alt="" height="260" src="#(y.thUrl)?x-oss-process=image/resize,h_200,m_lfit" style="margin: 5px;"
                 width="200"/>
            #end
            #end
        </td>
        <td>#(x.fv7??)</td>
        <td>#(x.fv6??)</td>
        <td>#(x.status??)</td>
        <td>#(x.ft4??)</td>
    </tr>
    #end
    </tbody>
</table>

</body>
</html>
