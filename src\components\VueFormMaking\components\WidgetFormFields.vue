<template>
  <div>
    <template v-if="element.type == 'input'">
      <el-input
        v-model="element.options.defaultValue"
        :disabled="element.options.disabled"
        :placeholder="element.options.placeholder"
        :style="{width: element.options.width}"
      />
    </template>

    <template v-if="element.type == 'textarea'">
      <el-input
        v-model="element.options.defaultValue"
        :disabled="element.options.disabled"
        :placeholder="element.options.placeholder"
        :rows="5"
        :style="{width: element.options.width}"
        type="textarea"
      />
    </template>

    <template v-if="element.type == 'number'">
      <el-input-number
        v-model="element.options.defaultValue"
        :controls-position="element.options.controlsPosition"
        :disabled="element.options.disabled"
        :style="{width: element.options.width}"
      />
    </template>

    <template v-if="element.type == 'radio'">
      <el-radio-group
        v-model="element.options.defaultValue"
        :disabled="element.options.disabled"
        :style="{width: element.options.width}"
      >
        <el-radio
          v-for="(item, index2) in element.options.options"
          :key="item.value + index2"
          :label="item.value"
          :style="{display: element.options.inline ? 'inline-block' : 'block'}"
        >
          {{ element.options.showLabel ? item.label : item.value }}
        </el-radio>
      </el-radio-group>
    </template>

    <template v-if="element.type == 'checkbox'">
      <el-checkbox-group
        v-model="element.options.defaultValue"
        :disabled="element.options.disabled"
        :style="{width: element.options.width}"
      >
        <el-checkbox
          v-for="(item, index1) in element.options.options"
          :key="item.value + index1"
          :label="item.value"
          :style="{display: element.options.inline ? 'inline-block' : 'block'}"
        >
          {{ element.options.showLabel ? item.label : item.value }}
        </el-checkbox>
      </el-checkbox-group>
    </template>

    <template v-if="element.type == 'time'">
      <el-time-picker
        v-model="element.options.defaultValue"
        :arrow-control="element.options.arrowControl"
        :clearable="element.options.clearable"
        :disabled="element.options.disabled"
        :editable="element.options.editable"
        :end-placeholder="element.options.endPlaceholder"
        :is-range="element.options.isRange"
        :placeholder="element.options.placeholder"
        :readonly="element.options.readonly"
        :start-placeholder="element.options.startPlaceholder"
        :style="{width: element.options.width}"
      />
    </template>

    <template v-if="element.type == 'date'">
      <el-date-picker
        v-model="element.options.defaultValue"
        :clearable="element.options.clearable"
        :disabled="element.options.disabled"
        :editable="element.options.editable"
        :end-placeholder="element.options.endPlaceholder"
        :is-range="element.options.isRange"
        :placeholder="element.options.placeholder"
        :readonly="element.options.readonly"
        :start-placeholder="element.options.startPlaceholder"
        :style="{width: element.options.width}"
        :type="element.options.type"
      />
    </template>

    <template v-if="element.type == 'rate'">
      <el-rate
        v-model="element.options.defaultValue"
        :allow-half="element.options.allowHalf"
        :disabled="element.options.disabled"
        :max="element.options.max"
      />
    </template>

    <template v-if="element.type == 'color'">
      <el-color-picker
        v-model="element.options.defaultValue"
        :disabled="element.options.disabled"
        :show-alpha="element.options.showAlpha"
      />
    </template>

    <template v-if="element.type == 'select'">
      <el-select
        v-model="element.options.defaultValue"
        :clearable="element.options.clearable"
        :disabled="element.options.disabled"
        :multiple="element.options.multiple"
        :placeholder="element.options.placeholder"
        :style="{width: element.options.width}"
      >
        <el-option
          v-for="item in element.options.options"
          :key="item.value"
          :label="element.options.showLabel?item.label:item.value"
          :value="item.value"
        />
      </el-select>
    </template>

    <template v-if="element.type=='switch'">
      <el-switch
        v-model="element.options.defaultValue"
        :disabled="element.options.disabled"
      />
    </template>

    <template v-if="element.type=='slider'">
      <el-slider
        v-model="element.options.defaultValue"
        :disabled="element.options.disabled"
        :max="element.options.max"
        :min="element.options.min"
        :range="element.options.range"
        :show-input="element.options.showInput"
        :step="element.options.step"
        :style="{width: element.options.width}"
      />
    </template>

    <template v-if="element.type=='imgupload'">
      <fm-upload
        v-model="element.options.defaultValue"
        :disabled="element.options.disabled"
        :height="element.options.size.height"
        :style="{'width': element.options.width}"
        :width="element.options.size.width"
        domain="xxx"
        token="xxx"
      />
    </template>

    <template v-if="element.type=='file'">
      <FileUpload :element="element" />
    </template>
    <template v-if="element.type=='ossfile'">
      <oss-file :element="element" />
    </template>

    <template v-if="element.type == 'cascader'">
      <el-cascader
        v-model="element.options.defaultValue"
        :clearable="element.options.clearable"
        :disabled="element.options.disabled"
        :options="element.options.remote?element.options.remoteOptions:element.options.options"
        :placeholder="element.options.placeholder"
        :show-all-levels="element.options.showAllLevels"
        :style="{width: element.options.width}"
      />
    </template>

    <template v-if="element.type == 'editor'">
      <vue-editor
        v-model="element.options.defaultValue"
        :style="{width: element.options.width}"
      />
    </template>

    <template v-if="element.type=='blank'">
      <div style="height: 50px;color: #999;background: #eee;line-height:50px;text-align:center;">
        {{ $t('fm.components.fields.blank') }}
      </div>
    </template>

    <template v-if="element.type === 'text'">
      <span
        :style="{
          'font-size': element.options.font_size,
          'font-family': element.options.font_family,
          'font-weight': element.options.font_weight,
          'color': element.options.font_color
        }"
      >
        {{ element.options.defaultValue }}
      </span>
    </template>

    <template v-if="element.type === 'divider'">
      <el-divider
        :content-position="element.options.content_position"
        :direction="element.options.direction"
      >
        <span
          :style="{
            'font-size': element.options.font_size,
            'font-family': element.options.font_family,
            'font-weight': element.options.font_weight,
            'color': element.options.font_color
          }"
        >
          {{ element.options.defaultValue }}
        </span>
      </el-divider>
    </template>

    <template v-if="element.type === 'treeSelect'">
      <Treeselect :element="element" :ishandelelayfun="ishandelelayfun" />
    </template>
    <template v-if="element.type === 'auxiliary'">
      <span>辅助字段将在表单中隐藏</span>
    </template>
  </div>
</template>

<script>
import FmUpload from './Upload'
import FileUpload from './Upload/file'
import Treeselect from './treeSelect/treeSelect'
import OssFile from './Upload/ossFile'

export default {
  name: 'WidgetFormFields',
  /* eslint-disable */
	props: ['element'],
	components: {
		FmUpload,
		FileUpload,
		Treeselect,
		OssFile
	},
	data() {
		return {
			ishandelelayfun: true
		}
	}
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
::v-deep .vue-treeselect__control, ::v-deep .vue-treeselect__placeholder, ::v-deep .vue-treeselect__single-value {
	height: 30px;
	line-height: 30px;
}

.user-home {
	::v-deep.el-tree {
		padding: 10px 0 !important;
	}
}
</style>
