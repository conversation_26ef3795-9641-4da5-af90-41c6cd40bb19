import request from '@/utils/request'
import qs from 'qs';

const COMMONURL = 'api/oaPmTaskRelated'

// 查询模板
export function getTaskRelated(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}?${query}`,
    method: 'get'
  })
}

export function getRelatedByPmName(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}/countGroupByPmName?${query}`,
    method: 'get'
  })
}

export function addTaskRelated(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'post',
    data
  })
}

export function delTaskRelated(ids) {
  return request({
    url: `${COMMONURL}`,
    method: 'delete',
    data: ids
  })
}

export function editTaskRelated(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'put',
    data
  })
}
