import request from '@/utils/request'
import qs from 'qs'
// 错误报告
export function get(params) {
  return request({
    url: 'api/omFaultReport',
    method: 'get',
    params
  })
}
export function getSmall(params) {
  const url = 'api/omFaultReport/small';
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: 'api/omFaultReport',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/omFaultReport',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/omFaultReport',
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: 'api/omFaultReport/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'api/omFaultReport/update/relation',
    method: 'put',
    data
  })
}

export function updateAssetId(data) {
  return request({
    url: 'api/omFaultReport/update/assetId',
    method: 'put',
    data
  })
}

export function updateAssetManualId(data) {
  return request({
    url: 'api/omFaultReport/update/manual/assetId',
    method: 'put',
    data
  })
}

export default { getSmall, get, add, edit, del, updateFormStruct, updateRelation, updateAssetId, updateAssetManualId }
