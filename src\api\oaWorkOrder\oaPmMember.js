import request from '@/utils/request'
import qs from 'qs'

export function get(params) {
  return request({
    url: 'api/oaPmMember' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: 'api/oaPmMember',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/oaPmMember',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/oaPmMember',
    method: 'put',
    data
  })
}

export default { get, add, edit, del }
