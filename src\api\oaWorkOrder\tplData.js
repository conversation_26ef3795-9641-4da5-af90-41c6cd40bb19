import request from '@/utils/request'
// import qs from 'qs'
// 查询模板
export function getTplData(params) {
  return request({
    url: 'api/oaWorkOrderTplData',
    method: 'get',
    params
  })
}

export function addTplData(data) {
  return request({
    url: 'api/oaWorkOrderTplData',
    method: 'post',
    data
  })
}

export function delTplData(ids) {
  return request({
    url: 'api/oaWorkOrderTplData',
    method: 'delete',
    data: ids
  })
}

export function editTplData(data) {
  return request({
    url: 'api/oaWorkOrderTplData',
    method: 'put',
    data
  })
}

export default { getTplData, addTplData, editTplData, delTplData }
