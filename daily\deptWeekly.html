<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
    <meta charset="UTF-8">
    <title>部门周报信息</title>
    <style type="text/css">
        body, div, table, thead, tbody, tfoot, tr, th, td, p {
            font-family: "宋体";
            font-size: small
        }

        .td-tblr {
            border: 1px solid #000000;
        }

        .break-content {
            white-space: pre-wrap;
            text-align: left;
        }
    </style>
    <script>
			function getUrlParams() {
				const url = window.location.href;
				const queryString = url.split('?')[1];
				if (!queryString) return {};

				const params = {};
				queryString.split('&').forEach(param => {
					const [key, value] = param.split('=');
					params[decodeURIComponent(key)] = decodeURIComponent(value);
				});
				return params;
			}

			function setPageTitle(params) {
				const title = `${ params.weekIndex }部门周报` || '部门周报';
				const titleElement = document.getElementById('pageTitle');
				if (titleElement) {
					titleElement.textContent = title;
				}
			}

			window.onload = function () {
				const urlParams = getUrlParams();
				setPageTitle(urlParams);
			};
    </script>
</head>
<body>
<h2 id="pageTitle" style="text-align:center;"></h2>
<table border="0" cellspacing="0" style='border-collapse: collapse;'>
    <colgroup>
        <col width="30">
        <col width="380">
        <col width="200">
        <col width="200">
        <col width="80">
        <col width="80">
        <col width="120">
        <col width="120">
        <col width="1000">
    </colgroup>
    <tr>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>&#x5e8f;&#x53f7;</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>&#21608;&#25253;&#21608;&#26399;</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>&#19978;&#25253;&#26085;&#26399;</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>&#21517;&#31216;</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle>
            <b>&#x8d1f;&#x8d23;&#x4eba;</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>&#22635;&#25253;&#20154;</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>&#24037;&#20316;&#20998;&#39033;</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>&#24037;&#20316;&#20998;&#31867;</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>&#24037;&#20316;&#23436;&#25104;&#24773;&#20917;</b>
        </td>
    </tr>
    <!-- 数据填充循环开始 -->
    #for(x : oaPmWeeklyReport.queryAll(mixQuery.weeklyReport??))
    <tr>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(for.count)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle>
            #(x.fv3??) - #(x.fv4??)
            #if(x.fv11??)
            (第#(x.fv11??)周)
            #end
        </td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.createTime??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv1??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.oaPmTree.createBy??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.createBy??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv6??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv7??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr break-content" valign=middle>
            #(x.fv8.replaceAll("\n", "<br/>"))
        </td>
    </tr>
    #end
    <!-- 数据填充循环结束 -->
</table>
</body>
</html>
