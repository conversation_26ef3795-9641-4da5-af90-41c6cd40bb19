<template>
  <div>
    <el-dialog title="地图展示" :visible.sync="dialogVisible" width="90%" :before-close="handleClose" class="map-dialog">
      <div class="map-container">
        <div id="amapContainer" class="amap-container" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 里程碑抽屉 -->
    <el-drawer
      :title="milestoneDrawerTitle"
      :visible.sync="milestoneDrawerVisible"
      direction="rtl"
      size="50%"
      :before-close="handleMilestoneDrawerClose"
    >

      <!-- 里程碑列表 -->
      <div class="milestone-table">
        <el-table :data="milestoneList" style="width: 100%">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="里程碑名称" min-width="120" />
          <el-table-column label="计划日期" min-width="180">
            <template slot-scope="scope">
              {{ formatDateRange(scope.row.planStartDate, scope.row.planEndDate) }}
            </template>
          </el-table-column>
          <el-table-column label="实际日期" min-width="180">
            <template slot-scope="scope">
              {{ formatDateRange(scope.row.realStartDate, scope.row.realEndDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="weight" label="权重" width="80">
            <template slot-scope="scope">
              <span>{{ !scope.row.weight.toString().includes('%') ? scope.row.weight + '%' :
                scope.row.weight }}</span>
            </template>
          </el-table-column>
          <el-table-column label="进度" width="180">
            <template slot-scope="scope">
              <el-progress :percentage="scope.row.progress" :show-text="true" :stroke-width="8" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" @click="viewControlPoints(scope.row)">
                查看管控点
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 里程碑分页器 -->
      <div class="milestone-pagination">
        <el-pagination
          :current-page="milestonePage.page"
          :page-size="milestonePage.size"
          :page-sizes="milestonePage.sizes"
          :total="milestonePage.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleMilestonePagination({ page: milestonePage.page, limit: $event })"
          @current-change="handleMilestonePagination({ page: $event, limit: milestonePage.size })"
        />
      </div>
    </el-drawer>

    <!-- 管控点模态框 -->
    <el-dialog
      title="管控点列表"
      :visible.sync="controlPointDialogVisible"
      width="60%"
      :before-close="handleControlPointDialogClose"
    >
      <el-table v-loading="controlPointLoading" :data="controlPointList" style="width: 100%">
        <el-table-column prop="name" label="名称" min-width="120" />
        <el-table-column label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getControlPointStatusType(scope.row.status)">
              {{ getControlPointStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="完成情况" min-width="150" />
        <el-table-column prop="weight" label="权重" width="80">
          <template slot-scope="scope">
            <span>{{ !scope.row.weight.toString().includes('%') ? scope.row.weight + '%' :
              scope.row.weight }}</span>
          </template>
        </el-table-column>
        <el-table-column label="附件" width="80" align="center">
          <template slot-scope="scope">
            <a v-if="scope.row.attachmentUrl" style="color: #409eff;" :href="scope.row.attachmentUrl">附件下载</a>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleControlPointDialogClose">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMilestoneList,
  getStepList
} from '@/api/oaWorkOrder/oaPmProgress'
export default {
  name: 'MapDialog',
  data() {
    return {
      dialogVisible: false,
      map: null,
      AMap: null,
      locationPoints: [], // 存储点位数据

      // 里程碑抽屉相关
      milestoneDrawerVisible: false,
      milestoneList: [],
      milestoneLoading: false,
      currentPoint: null, // 当前选中的点位

      // 管控点模态框相关
      controlPointDialogVisible: false,
      controlPointList: [],
      controlPointLoading: false,
      currentMilestone: null, // 当前选中的里程碑

      // 里程碑分页数据
      milestonePage: {
        page: 1,
        size: 10,
        total: 0,
        sizes: [10, 20, 30, 50]
      }
    }
  },

  computed: {
    // 里程碑抽屉标题
    milestoneDrawerTitle() {
      if (this.currentPoint && this.currentPoint.name) {
        return `里程碑列表【${this.currentPoint.name}】`
      }
      return '里程碑列表'
    }
  },

  beforeDestroy() {
    if (this.map) {
      this.map.destroy()
      this.map = null
    }
  },
  methods: {
    // 里程碑分页处理
    handleMilestonePagination(pagination) {
      this.milestonePage.page = pagination.page
      this.milestonePage.size = pagination.limit
      this.loadMilestoneList()
    },
    // 获取管控点状态类型
    getControlPointStatusType(status) {
      const statusMap = {
        1: 'info',
        2: 'warning',
        3: 'success'
      }
      return statusMap[status] || 'info'
    },

    // 获取管控点状态文本
    getControlPointStatusText(status) {
      const statusMap = {
        1: '未开始',
        2: '进行中',
        3: '已完成'
      }
      return statusMap[status] || '未知'
    },

    // 根据状态获取点位图标
    getMarkerIcon(status) {
      // 使用可靠的图标URL或base64编码的图标
      const iconMap = {
        1: {
          // 未开始：灰色图标
          image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMzQiIHZpZXdCb3g9IjAgMCAyNCAzNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNy4wNTg4OCAyIDMgNi4wNTg4OCAzIDExQzMgMTcuNSAxMiAzMCAxMiAzMEMxMiAzMCAyMSAxNy41IDIxIDExQzIxIDYuMDU4ODggMTYuOTQxMSAyIDEyIDJaIiBmaWxsPSIjOTk5OTk5IiBzdHJva2U9IiNmZmZmZmYiIHN0cm9rZS13aWR0aD0iMiIvPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjExIiByPSI0IiBmaWxsPSIjZmZmZmZmIi8+Cjwvc3ZnPgo=',
          size: [24, 34],
          anchor: [12, 34]
        },
        2: {
          // 进行中：蓝色图标
          image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMzQiIHZpZXdCb3g9IjAgMCAyNCAzNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNy4wNTg4OCAyIDMgNi4wNTg4OCAzIDExQzMgMTcuNSAxMiAzMCAxMiAzMEMxMiAzMCAyMSAxNy41IDIxIDExQzIxIDYuMDU4ODggMTYuOTQxMSAyIDEyIDJaIiBmaWxsPSIjNDA5ZWZmIiBzdHJva2U9IiNmZmZmZmYiIHN0cm9rZS13aWR0aD0iMiIvPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjExIiByPSI0IiBmaWxsPSIjZmZmZmZmIi8+Cjwvc3ZnPgo=',
          size: [24, 34],
          anchor: [12, 34]
        },
        3: {
          // 已完成：绿色图标
          image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMzQiIHZpZXdCb3g9IjAgMCAyNCAzNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNy4wNTg4OCAyIDMgNi4wNTg4OCAzIDExQzMgMTcuNSAxMiAzMCAxMiAzMEMxMiAzMCAyMSAxNy41IDIxIDExQzIxIDYuMDU4ODggMTYuOTQxMSAyIDEyIDJaIiBmaWxsPSIjNjdjMjNhIiBzdHJva2U9IiNmZmZmZmYiIHN0cm9rZS13aWR0aD0iMiIvPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjExIiByPSI0IiBmaWxsPSIjZmZmZmZmIi8+Cjwvc3ZnPgo=',
          size: [24, 34],
          anchor: [12, 34]
        }
      }
      return iconMap[status] || iconMap[1] // 默认返回灰色图标
    },
    // 显示弹窗
    show(points) {
      this.dialogVisible = true
      // 如果传入了点位数据，则保存
      if (points && Array.isArray(points)) {
        this.locationPoints = points
      }
      this.$nextTick(() => {
        this.initMap()
      })
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      if (this.map) {
        this.map.destroy()
        this.map = null
      }
    },

    // 初始化地图
    initMap() {
      // 检查是否已经加载了高德地图API
      if (window.AMap) {
        this.createMap()
      } else {
        this.loadAmapScript().then(() => {
          this.createMap()
        })
      }
    },

    // 动态加载高德地图API
    loadAmapScript() {
      return new Promise((resolve, reject) => {
        if (window.AMap) {
          resolve()
          return
        }

        const script = document.createElement('script')
        script.type = 'text/javascript'
        script.async = true
        // 使用您申请的高德地图API key
        script.src = 'https://webapi.amap.com/maps?v=2.0&key=4b07dc461e63490e8f1079a6b63ee945'
        script.onload = () => {
          resolve()
        }
        script.onerror = () => {
          reject(new Error('高德地图API加载失败'))
        }
        document.head.appendChild(script)
      })
    },

    // 创建地图实例
    createMap() {
      try {
        // 销毁之前的地图实例
        if (this.map) {
          this.map.destroy()
        }

        // 创建地图实例
        // eslint-disable-next-line no-undef
        this.map = new AMap.Map('amapContainer', {
          zoom: 13, // 缩放级别
          center: this.locationPoints && this.locationPoints.length > 0 ? [this.locationPoints[0].longitude, this.locationPoints[0].latitude] : [116.397428, 39.90923], // 地图中心点（北京）
          mapStyle: 'amap://styles/normal', // 地图样式
          viewMode: '2D', // 地图模式
          lang: 'zh_cn', // 地图语言
          resizeEnable: true, // 是否监控地图容器尺寸变化
          rotateEnable: true, // 是否允许地图旋转
          pitchEnable: true, // 是否允许设置俯仰角度
          buildingAnimation: true, // 楼块出现是否带动画
          expandZoomRange: true, // 是否支持可以扩展最大缩放级别
          dragEnable: true, // 是否可拖拽
          zoomEnable: true, // 是否可缩放
          doubleClickZoom: true, // 是否可双击放大
          keyboardEnable: true, // 是否可通过键盘控制
          jogEnable: true, // 是否启用地图惯性移动
          scrollWheel: true, // 是否支持滚轮缩放
          touchZoom: true, // 是否支持多点触控缩放
          touchZoomCenter: 1 // 可缺省，当touchZoomCenter=1的时候，手机端双指缩放的以地图中心为中心，否则默认以双指中间点为中心
        })

        // 添加地图控件
        this.addMapControls()

        // 地图加载完成后的回调
        this.map.on('complete', () => {
          console.log('地图加载完成')
          // 添加标记点
          this.addMarkers()
        })
      } catch (error) {
        console.error('创建地图失败:', error)
        this.$message.error('地图初始化失败，请稍后重试')
      }
    },

    // 添加地图控件
    addMapControls() {
      // 使用高德地图API 2.0的异步加载方式
      // eslint-disable-next-line no-undef
      AMap.plugin([
        'AMap.Scale',
        'AMap.OverView',
        'AMap.ToolBar',
        'AMap.MapType'
      ], () => {
        try {
          // eslint-disable-next-line no-undef
          // 添加比例尺控件
          this.map.addControl(new AMap.Scale())

          // eslint-disable-next-line no-undef
          // 添加鹰眼控件
          this.map.addControl(new AMap.OverView({
            isOpen: false
          }))

          // eslint-disable-next-line no-undef
          // 添加工具条控件
          this.map.addControl(new AMap.ToolBar())

          // eslint-disable-next-line no-undef
          // 添加地图类型切换控件
          this.map.addControl(new AMap.MapType({
            defaultType: 0,
            showTraffic: false,
            showRoad: false
          }))
        } catch (error) {
          console.error('添加地图控件失败:', error)
          // 控件添加失败不影响地图基本功能，只记录错误
        }
      })
    },

    // 添加标记点
    addMarkers() {
      if (!this.locationPoints || this.locationPoints.length === 0) {
        console.log('没有点位数据')
        return
      }

      // 创建点标记
      const markers = []
      const bounds = []

      this.locationPoints.forEach(point => {
        // 获取图标配置
        const iconConfig = this.getMarkerIcon(point.status)

        // 创建标记点
        // eslint-disable-next-line no-undef
        const marker = new AMap.Marker({
          position: [point.longitude, point.latitude],
          title: point.name,
          // 根据状态设置标记点的图标样式
          icon: new AMap.Icon({
            image: iconConfig.image,
            size: new AMap.Size(iconConfig.size[0], iconConfig.size[1]),
            imageSize: new AMap.Size(iconConfig.size[0], iconConfig.size[1]),
            anchor: new AMap.Pixel(iconConfig.anchor[0], iconConfig.anchor[1])
          }),
          // 设置是否可拖拽
          draggable: false,
          // 设置点标记是否可见
          visible: true,
          // 设置点标记的叠加顺序
          zIndex: 100,
          // 设置点标记的动画效果
          animation: 'AMAP_ANIMATION_DROP',
          // 设置点标记是否可点击
          clickable: true
        })

        // 将标记点添加到地图
        marker.setMap(this.map)
        markers.push(marker)
        bounds.push([point.longitude, point.latitude])

        // 绑定点击事件 - 打开里程碑抽屉
        marker.on('click', () => {
          this.openMilestoneDrawer(point)
        })
      })

      // 如果有多个点位，自动调整地图视野以包含所有标记点
      if (bounds.length > 0) {
        this.map.setFitView(markers)
      }
    },

    // 打开里程碑抽屉
    async openMilestoneDrawer(point) {
      this.currentPoint = point
      this.milestoneDrawerVisible = true
      await this.loadMilestoneList()
    },

    // 关闭里程碑抽屉
    handleMilestoneDrawerClose() {
      this.milestoneDrawerVisible = false
      this.milestoneList = []
      this.currentPoint = null
    },

    // 加载里程碑列表
    async loadMilestoneList() {
      if (!this.currentPoint) return

      this.milestoneLoading = true
      try {
        const params = {
          pointId: this.currentPoint.id,
          page: this.milestonePage.page - 1, // 后端页码从0开始
          size: this.milestonePage.size
        }

        const response = await getMilestoneList(params)
        this.milestoneList = response.content || []
        this.milestonePage.total = response.totalElements || 0
      } catch (error) {
        console.error('加载里程碑列表失败:', error)
        this.$message.error('加载里程碑列表失败')
      } finally {
        this.milestoneLoading = false
      }
    },

    // 查看管控点
    async viewControlPoints(milestone) {
      this.currentMilestone = milestone
      this.controlPointDialogVisible = true
      await this.loadControlPointList()
    },

    // 关闭管控点模态框
    handleControlPointDialogClose() {
      this.controlPointDialogVisible = false
      this.controlPointList = []
      this.currentMilestone = null
    },

    // 加载管控点列表
    async loadControlPointList() {
      if (!this.currentMilestone) return

      this.controlPointLoading = true
      try {
        const params = {
          milestoneId: this.currentMilestone.id
        }

        const response = await getStepList(params)
        this.controlPointList = response || []
      } catch (error) {
        console.error('加载管控点列表失败:', error)
        this.$message.error('加载管控点列表失败')
      } finally {
        this.controlPointLoading = false
      }
    },

    // 格式化日期范围
    formatDateRange(startDate, endDate) {
      // 格式化单个日期，去掉时分秒
      const formatDate = (date) => {
        if (!date) return null
        // 如果日期包含时分秒，只取日期部分
        if (typeof date === 'string' && date.includes(' ')) {
          return date.split(' ')[0]
        }
        return date
      }

      const formattedStartDate = formatDate(startDate)
      const formattedEndDate = formatDate(endDate)

      if (!formattedStartDate && !formattedEndDate) return '-'
      if (!formattedStartDate) return `- ~ ${formattedEndDate}`
      if (!formattedEndDate) return `${formattedStartDate} ~ -`
      return `${formattedStartDate} ~ ${formattedEndDate}`
    }
  }
}
</script>

<style lang="scss" scoped>
// 里程碑分页器样式
.milestone-pagination {
  margin-top: 20px;
  text-align: right;
}

.map-dialog {
  ::v-deep .el-dialog {
    margin-top: 10vh !important;
    max-height: 80vh;
  }

  ::v-deep .el-dialog__body {
    padding: 10px 20px;
  }
}

.map-container {
  width: 100%;
  height: 60vh;
  position: relative;
}

.amap-container {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

// 抽屉样式
.milestone-drawer-content {
  padding: 20px;
}

// 抽屉内表格样式
::v-deep .el-drawer__body {
  padding: 0;
}

// 进度条样式调整
::v-deep .el-progress-bar__outer {
  border-radius: 4px;
}

::v-deep .el-progress-bar__inner {
  border-radius: 4px;
}

// 模态框内表格样式
::v-deep .el-dialog .el-table {
  .el-progress {
    width: 100%;
  }
}
</style>
