<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
    <meta charset="UTF-8">
    <title>项目信息</title>
    <style type="text/css">
        body, div, table, thead, tbody, tfoot, tr, th, td, p {
            font-family: "宋体";
            font-size: small
        }

        .td-tblr {
            border: 1px solid #000000;
            max-width: 250px; /* 限制最大宽度 */
            min-width: 180px; /* 设置最小宽度 */
        }

        .td-tblr-long {
            border: 1px solid #000000;
            max-width: 300px; /* 限制最大宽度 */
            min-width: 200px; /* 设置最小宽度 */
        }

        .break-content {
            white-space: pre-wrap;
            text-align: left;
        }
    </style>
</head>
<body>
<h2 style="text-align:center;">
    项目列表
</h2>
<table border="0" cellspacing="0" style='border-collapse: collapse;'>
    <colgroup>
        <col width="300">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
        <col width="200">
    </colgroup>
    <tr>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr-long" valign=middle><b>项目名称</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>部门</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>项目类型</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>项目负责人</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>项目分类(一)</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>项目分类(二)</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>项目阶段</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>项目状态</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>毛利率</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>收入(元)</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>已收入金额(元)</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>未收入(元)</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>预算支出(元)</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>已支出费用(元)</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>未支出费用(元)</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>预算结余</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>合同开始日期</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>合同结束日期</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>终验日期</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>质保日期</b></td>
        <td align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle><b>创建日期</b></td>
    </tr>
    <!-- 数据填充循环开始 -->
    #for(x : oaPmTree.query(mixQuery.pmTree??,pageable??).content)
    <tr>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.name??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.ft4??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.extend.data['2']??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.extend.data['3']??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.cateNames.size() > 0 ? x.cateNames[0]??
                                                                            : "")
        </td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.cateNames.size() > 1 ? x.cateNames[1]??
                                                                            : "")
        </td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.extend.data['8']??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.extend.data['9']??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv14??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv7??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv8??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv9??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv11??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv12??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv21??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv13??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv15??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv16??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv17??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.fv20??)</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle> #(x.createTime??)</td>
    </tr>
    #end
    <!-- 数据填充循环结束 -->
</table>
</body>
</html>
