<template>
  <el-form-item
    v-if="showStatus"
    :label="isLabel === false || widget.type === 'divider' || !widget.options.labelWidthStatus ? '' : widget.name"
    :label-width="isLabel === false || !widget.options.labelWidthStatus ? '0px' : widgetLabelWidth + 'px'"
    :prop="propValue"
    :style="{
      display: widget.name === '序号' ? 'none' : '',
      ...(subformIndex !== undefined ? { 'margin-bottom': '0' } : {})
    }"
  >
    <template v-if="preview">
      <!-- 树组件 -->
      <template v-if="widget.type === 'treeSelect'">
        <Treeselect :data-model="dataModel" :element="widget" :preview="preview" :remote="remote" />
      </template>
      <template v-else-if="widget.type === 'select'">
        <!--{{ widget.options.remoteOptions.find(item => item.value === dataModel).label || dataModel    }}-->
        {{ selectShow(dataModel) }}
      </template>
      <template v-else-if="widget.type === 'color'">
        <div
          :style="{ 'background-color': dataModel }"
          style="width: 32px; height: 20px; margin-top: 6px; border-radius: 3px"
        />
      </template>
      <template v-else-if="widget.type == 'switch'">
        <el-switch v-model="dataModel" :disabled="true" />
      </template>
      <template v-else-if="widget.type === 'editor'">
        <div class="previewEditorDiv" v-html="dataModel" />
      </template>

      <template v-else-if="widget.type == 'file'">
        <div v-for="(uploadUrlItem, uploadUrlIndex) of dataModel" :key="uploadUrlIndex">
          <i class="el-icon-document" style="color: #909399;" />
          <a :href="uploadUrlItem.url" target="_blank" @click="download(uploadUrlItem)">{{ uploadUrlItem.name }}</a>
        </div>
      </template>
      <template v-else-if="widget.type == 'ossfile'">
        <div v-for="(uploadUrlItem, uploadUrlIndex) of dataModel" :key="uploadUrlIndex">
          <i class="el-icon-document" style="color: #909399;" />
          <a :href="uploadUrlItem.url" target="_blank" @click="openFile(uploadUrlItem)">{{ uploadUrlItem.name }}</a>
        </div>
      </template>

      <template v-else-if="widget.type == 'imgupload'">
        <fm-upload
          v-model="dataModel"
          :height="widget.options.size.height"
          :preview="preview"
          :style="{ 'width': widget.options.width }"
          :width="widget.options.size.width"
        />
      </template>
      <template v-else-if="widget.type == 'rate'">
        <el-rate
          v-model="dataModel"
          :allow-half="widget.options.allowHalf"
          :disabled="true"
          :max="widget.options.max"
        />
      </template>
      <template v-else-if="widget.type === 'divider'">
        <el-divider :content-position="widget.options.content_position" :direction="widget.options.direction">
          <span
            :style="{
              'font-size': widget.options.font_size,
              'font-family': widget.options.font_family,
              'font-weight': widget.options.font_weight,
              'color': widget.options.font_color
            }"
          >
            {{ widget.options.defaultValue }}
          </span>
        </el-divider>
      </template>
      <template v-else-if="widget.type === 'input' && widget.options.showPassword">
        <input
          :value="dataModel"
          disabled="disabled"
          style="border: none; background-color: #ffffff; color: #303133"
          type="password"
        >
      </template>
      <template v-else-if="widget.type === 'cascader'">
        <el-cascader
          v-model="dataModel"
          :disabled="true"
          :options="widget.options.remote ? widget.options.remoteOptions : widget.options.options"
          :show-all-levels="widget.options.showAllLevels"
          class="preview-cascader-class"
          placeholder=""
        />
      </template>
      <template v-else>
        <div>
          {{ dataModel }}
        </div>
      </template>
    </template>
    <template v-else>
      <template v-if="widget.type === 'input'">
        <el-input
          v-if="widget.options.dataType === 'number'
            || widget.options.dataType === 'integer'
            || widget.options.dataType === 'float'"
          v-model.number="dataModel"
          :disabled="widget.options.disabled"
          :placeholder="widget.options.placeholder"
          :show-password="widget.options.showPassword"
          :style="{ width: widget.options.width }"
          :type="widget.options.dataType"
        />
        <el-input
          v-else
          v-model="dataModel"
          :disabled="widget.options.disabled"
          :placeholder="widget.options.placeholder"
          :show-password="widget.options.showPassword"
          :style="{
            width: widget.options.width,
          }"
          :type="widget.options.dataType"
        />
      </template>

      <template v-if="widget.type === 'textarea'">
        <el-input
          v-model="dataModel"
          :disabled="widget.options.disabled"
          :placeholder="widget.options.placeholder"
          :rows="5"
          :style="{ width: widget.options.width }"
          type="textarea"
        />
      </template>

      <template v-if="widget.type === 'number'">
        <el-input-number
          v-model="dataModel"
          :disabled="widget.options.disabled"
          :step="widget.options.step"
          :style="{ width: widget.options.width }"
          controls-position="right"
        />
      </template>

      <template v-if="widget.type === 'radio'">
        <el-radio-group
          v-model="dataModel"
          :disabled="widget.options.disabled"
          :style="{ width: widget.options.width }"
        >
          <el-radio
            v-for="(item, index) in (widget.options.remote ? widget.options.remoteOptions : widget.options.options)"
            :key="index"
            :label="item.value"
            :style="{ display: widget.options.inline ? 'inline-block' : 'block' }"
          >
            <template v-if="widget.options.remote">{{ item.label }}</template>
            <template v-else>{{ widget.options.showLabel ? item.label : item.value }}</template>
          </el-radio>
        </el-radio-group>
      </template>

      <template v-if="widget.type === 'checkbox'">
        <el-checkbox-group
          v-model="dataModel"
          :disabled="widget.options.disabled"
          :style="{ width: widget.options.width }"
        >
          <el-checkbox
            v-for="(item, index) in (widget.options.remote ? widget.options.remoteOptions : widget.options.options)"
            :key="index"
            :label="item.value"
            :style="{ display: widget.options.inline ? 'inline-block' : 'block' }"
          >
            <template v-if="widget.options.remote">{{ item.label }}</template>
            <template v-else>{{ widget.options.showLabel ? item.label : item.value }}</template>
          </el-checkbox>
        </el-checkbox-group>
      </template>

      <template v-if="widget.type === 'time'">
        <el-time-picker
          v-model="dataModel"
          :arrow-control="widget.options.arrowControl"
          :clearable="widget.options.clearable"
          :disabled="widget.options.disabled"
          :editable="widget.options.editable"
          :end-placeholder="widget.options.endPlaceholder"
          :is-range="widget.options.isRange"
          :placeholder="widget.options.placeholder"
          :readonly="widget.options.readonly"
          :start-placeholder="widget.options.startPlaceholder"
          :style="{ width: widget.options.width }"
          :value-format="widget.options.format"
        />
      </template>

      <template v-if="widget.type == 'date'">
        <el-date-picker
          v-model="dataModel"
          :clearable="widget.options.clearable"
          :disabled="widget.options.disabled"
          :editable="widget.options.editable"
          :end-placeholder="widget.options.endPlaceholder"
          :format="widget.options.format"
          :placeholder="widget.options.placeholder"
          :readonly="widget.options.readonly"
          :start-placeholder="widget.options.startPlaceholder"
          :style="{ width: widget.options.width }"
          :type="widget.options.type"
          :value-format="widget.options.timestamp ? 'timestamp' : widget.options.format"
        />
      </template>

      <template v-if="widget.type == 'rate'">
        <el-rate
          v-model="dataModel"
          :allow-half="widget.options.allowHalf"
          :disabled="widget.options.disabled"
          :max="widget.options.max"
        />
      </template>

      <template v-if="widget.type === 'color'">
        <el-color-picker
          v-model="dataModel"
          :disabled="widget.options.disabled"
          :show-alpha="widget.options.showAlpha"
        />
      </template>

      <template v-if="widget.type === 'select'">
        <el-select
          v-model="dataModel"
          :clearable="widget.options.clearable"
          :disabled="widget.options.disabled"
          :filterable="widget.options.filterable"
          :multiple="widget.options.multiple"
          :placeholder="widget.options.placeholder"
          :remote="widget.options.remoteFilterable"
          :remote-method="remoteMethod"
          :reserve-keyword="widget.options.remoteFilterable"
          :style="{ width: widget.options.width }"
        >
          <el-option
            v-for="item in (widget.options.remote ? widget.options.remoteOptions : widget.options.options)"
            :key="item.value"
            :label="widget.options.showLabel || widget.options.remote ? item.label : item.value"
            :value="item.value"
          />
        </el-select>
      </template>

      <template v-if="widget.type == 'switch'">
        <el-switch v-model="dataModel" :disabled="widget.options.disabled" />
      </template>

      <template v-if="widget.type == 'slider'">
        <el-slider
          v-model="dataModel"
          :disabled="widget.options.disabled"
          :max="widget.options.max"
          :min="widget.options.min"
          :range="widget.options.range"
          :show-input="widget.options.showInput"
          :step="widget.options.step"
          :style="{ width: widget.options.width }"
        />
      </template>

      <template v-if="widget.type == 'imgupload'">
        <fm-upload
          v-model="dataModel"
          :action="`${getActionUrl(widget.options.action)}?name=${'1'}`"
          :disabled="widget.options.disabled"
          :domain="widget.options.domain"
          :height="widget.options.size.height"
          :is-delete="widget.options.isDelete"
          :is-edit="widget.options.isEdit"
          :is-qiniu="widget.options.isQiniu"
          :length="widget.options.length"
          :min="widget.options.min"
          :multiple="widget.options.multiple"
          :platform="widget.options.platform"
          :style="{ 'width': widget.options.width }"
          :thheight="widget.options.thSize.height"
          :thwidth="widget.options.thSize.height"
          :token="widget.options.token"
          :width="widget.options.size.width"
        />
        <!-- :action="`${widget.options.action}?name=${''}`" -->
      </template>

      <template v-if="widget.type == 'file'">
        <FileUpload :data-model="dataModel" :element="widget" @fileList="fileList" />
      </template>
      <template v-if="widget.type == 'ossfile'">
        <oss-file :data-model="dataModel" :element="widget" @fileList="fileList" />
      </template>

      <template v-if="widget.type === 'editor'">
        <vue-editor
          v-model="dataModel"
          :disabled="widget.options.disabled"
          :style="{ width: widget.options.width }"
          use-custom-image-handler
          @image-added="handleImageAdded"
        />
      </template>

      <template v-if="widget.type === 'cascader'">
        <el-cascader
          v-model="dataModel"
          :clearable="widget.options.clearable"
          :disabled="widget.options.disabled"
          :options="widget.options.remote ? widget.options.remoteOptions : widget.options.options"
          :placeholder="widget.options.placeholder"
          :show-all-levels="widget.options.showAllLevels"
          :style="{ width: widget.options.width }"
        />
      </template>

      <template v-if="widget.type === 'text'">
        <span
          :style="{
            'font-size': widget.options.font_size,
            'font-family': widget.options.font_family,
            'font-weight': widget.options.font_weight,
            'color': widget.options.font_color
          }"
        >
          {{ widget.options.defaultValue }}
        </span>
      </template>

      <template v-if="widget.type === 'divider'">
        <el-divider :content-position="widget.options.content_position" :direction="widget.options.direction">
          <span
            :style="{
              'font-size': widget.options.font_size,
              'font-family': widget.options.font_family,
              'font-weight': widget.options.font_weight,
              'color': widget.options.font_color
            }"
          >
            {{ widget.options.defaultValue }}
          </span>
        </el-divider>
      </template>
      <!-- 树组件 -->
      <template v-if="widget.type === 'treeSelect'">
        <Treeselect
          :data-model="dataModel"
          :element="widget"
          :preview="preview"
          :remote="remote"
          @changeTree="changeTree"
        />
      </template>
    </template>

  </el-form-item>
</template>

<script>
import Treeselect from './treeSelect/treeSelect'
import FmUpload from './Upload'
import FileUpload from './Upload/file'
import OssFile from './Upload/ossFile'
// import crudLocalStorage from '@/api/tools/localStorage'
// import { downloadFile } from '@/utils/index'
import { mapGetters } from 'vuex'
import numeric from 'numeric';
import { isString, isArray } from '@/utils/is'

export default {
  name: 'GenetateFormItem',
  components: {
    FmUpload,
    FileUpload,
    Treeselect,
    OssFile
  },
  /* eslint-disable */
  props: ['widget', 'models', 'propValue', 'remote', 'data', 'disabled', 'preview', 'isLabel', 'subformIndex', 'subformModel'],
  data() {
    return {
      showStatus: true,
      widgetLabelWidth: '',
      dataModel: this.subformIndex === undefined ?
        this.models[this.widget.model] :
        this.models[this.subformModel][this.subformIndex][this.widget.model],
      tableData: [],
      metadata: []
    }
  },
  computed: {
    ...mapGetters([
      'baseApi',
      'preViewUrl'
    ]),
    selectShow() {
      return (dataModel) => {
        let { multiple } = this.widget.options
        if (multiple) {
          if (isString(dataModel)) {
            return dataModel
          } else {
            return dataModel.join(',')
          }
        } else {
          return dataModel
        }
      }
    }
  },
  watch: {
    dataModel: {
      deep: true,
      handler(newValue) {
        let { name } = this.data.config
        let { model, type } = this.widget
        if (name == "newProjectForm") {
          this.handleProjectInfo(model, newValue)
        } else if (name == "xlAsset") {
          // this.handleXlAsset(model, newValue)
        }
        if (newValue !== undefined && newValue !== null) {
          // 对于 number 类型的字段，确保数据类型正确
          let processedValue = newValue;
          if (this.widget.options && this.widget.options.dataType === 'number' && typeof newValue === 'string') {
            const numValue = Number(newValue);
            if (!isNaN(numValue)) {
              processedValue = numValue;
            }
          }

          if (this.subformIndex !== undefined) {
            this.models[this.subformModel][this.subformIndex][this.widget.model] = processedValue
            this.$emit('update:models', {
              ...this.models,
              [this.subformModel]: this.models[this.subformModel]
            })
            this.$emit('input-change', processedValue, this.widget.model, this.subformIndex)
          } else {
            let { type } = this.widget
            if (type == 'cascader' || type == 'treeSelect') {
              if (processedValue) {
                this.$parent.clearValidate(this.widget.model)
              }
            }
            this.models[this.widget.model] = processedValue
            this.$emit('update:models', {
              ...this.models,
              [this.widget.model]: processedValue
            })
            this.$emit('input-change', processedValue, this.widget.model)
          }
        }
      }
    },
    models: {
      deep: true,
      handler(val) {
        if (val.status === undefined && val.status === null) {
          if (this.subformIndex === undefined) {
            this.dataModel = val[this.widget.model]
          } else {
            this.dataModel = val[this.subformModel][this.subformIndex][this.widget.model]
          }
        }
        delete this.models.status
        this.handleDisplayVerifiy()
      }
    }
  },
  created() {
    if (this.widget.options.remote && this.remote[this.widget.options.remoteFunc] && this.widget.type !== 'treeSelect') {
      this.remote[this.widget.options.remoteFunc]((data) => {
        this.metadata = data
        this.widget.options.remoteOptions = data.map(item => {
          return {
            value: item[this.widget.options.props.value],
            label: item[this.widget.options.props.label],
            children: item[this.widget.options.props.children]
          }
        })
      }, {})
    }

    if (this.widget.type === 'imgupload' && this.widget.options.isQiniu) {
      this.remote[this.widget.options.tokenFunc]((data) => {
        this.widget.options.token = data
      })
    }
    if (this.widget.type === 'imgupload') {
      this.dataModel = isArray(this.dataModel) ? this.dataModel : []
    }

    if (this.disabled !== undefined && this.disabled !== null) {
      this.widget.options.disabled = this.disabled
    }

    // label width
    if (this.widget.options.labelWidthDisabled) {
      this.widgetLabelWidth = this.widget.options.labelWidth
    } else if (this.widget.type === 'divider') {
      this.widgetLabelWidth = 0
    } else {
      this.widgetLabelWidth = this.data.config.labelWidth
    }
    this.handleDisplayVerifiy()
  },
  methods: {
    // 动态替换action地址，将线上地址改为本地地址
    getActionUrl(originalAction) {
      if (!originalAction) return originalAction;

      // 如果是线上地址，替换为本地地址
      if (originalAction.includes('routine.fatoan.com')) {
        return originalAction.replace('https://routine.fatoan.com', this.$store.getters.baseApi);
      }

      return originalAction;
    },
    openFile(file) {
      const url = file.raw.response.url;
      window.open(`${this.preViewUrl}/onlinePreview?url=${encodeURIComponent(btoa(url))}`);
    },
    handleImageAdded: function (file, Editor, cursorLocation, resetUploader) {
      var formData = new FormData();
      formData.append("file", file); //第一个file 后台接收的参数名
      this.$axios({
        url: "/api/localStorage?name=",//上传路径
        method: "POST",
        data: formData,
      }).then(result => {
        // console.log(result);
        let url = this.baseApi + '/file/' + result.data.type + '/' + result.data.realName
        Editor.insertEmbed(cursorLocation, "image", url);
        resetUploader();
      })
        .catch(err => {
          console.log(err);
        });
    },
    // download(item) {
    // 	const loading = this.$loading({
    // 		lock: true,
    // 		text: '下载中',
    // 		spinner: 'el-icon-loading',
    // 		background: 'rgba(0, 0, 0, 0.7)'
    // 	});
    // 	crudLocalStorage.downloadFile(item.response).then((res) => {
    // 		loading.close();
    // 		downloadFile(res, item.name, item.type)
    // 	}).catch(e => {
    // 		loading.close();
    // 	});
    // },
    fileList(files) {
      this.dataModel = files
    },
    changeTree(val) {
      this.dataModel = val;
    },
    handleDisplayVerifiy() {
      if (Object.keys(this.widget.options).indexOf('displayVerifiy') >= 0) {
        if (this.widget.options.displayVerifiy.type !== 'hide') {
          var c = 0
          for (var v of this.widget.options.displayVerifiy.list) {
            if (this.models[v.model] && this.models[v.model].toString() === v.value) {
              c++
            }
          }
          if (this.widget.options.displayVerifiy.type === 'and') {
            if (c !== this.widget.options.displayVerifiy.list.length) {
              this.showStatus = false
            } else {
              this.showStatus = true
            }
          } else if (this.widget.options.displayVerifiy.type === 'or') {
            if (c === 0) {
              this.showStatus = false
            } else {
              this.showStatus = true
            }
          }
        }
      }
    },
    // 处理远程搜索
    remoteMethod(query) {
      console.log(query, '<===>', 'query')
      if (this.widget.options.remote && this.remote[this.widget.options.remoteFunc] && this.widget.type !== 'treeSelect') {
        this.remote[this.widget.options.remoteFunc]((data) => {
          console.log(data, '<===>', 'data')
          this.metadata = data
          this.widget.options.remoteOptions = data.map(item => {
            return {
              value: item[this.widget.options.props.value],
              label: item[this.widget.options.props.label],
              children: item[this.widget.options.props.children]
            }
          })
        }, { query })
      }

    },
    // 对雪亮资产信息进行特殊处理
    handleXlAsset(model, dataModel) {

    },
    // 对雪亮资产信息进行特殊处理 赋值处理
    // handleXlAssetAssign(dataModel, type, key, self) {
    // 	let flag = this.metadata.some(item => item.self)
    // 	if (flag) {
    // 		// 有一个符合条件 则是true
    // 		return {[key]: self.dataModel}
    // 	} else {
    // 		let obj = this.metadata.find(item => item[type] === dataModel)
    // 		return obj
    // 	}
    // },

    // 	对项目信息进行特殊处理
    handleProjectInfo(model, dataModel) {
      console.log(model, dataModel, '<===>', 'model')
      // 这儿数字表示model
      // 14 总金额
      // 12 立项阶段收入
      // 13 是立项预算支出

      // 总金额 = 立项阶段收入  14 = 12
      // 未收入金额 = 立项阶段收入 - 已收入金额 16 = 12 - 15
      // 预算结余=采购预算+费用预算-采购签订合同额-预算费用支出 19 = (13 + 28) - (29 + 30)
      // 采购签订合同额 = 已支出采购合同额+未支出采购合同额 29 = 17 + 27
      // 毛利率 = (立项阶段收入金额-立项阶段预算支出)/立项阶段收入金额 *100%  四舍五入保留俩位小数 24 = (12-13)/12 * 100

      let key = {
        incomeAtTheProjectApprovalStage: '12', //立项阶段收入
        projectBudgetExpenditure: '13', // 立项预算支出 --- 采购预算
        totalAmount: '14', // 总金额
        theAmountThatHasBeenEarned: '15', // 已收入金额
        unfinishedAmountKey: '16', // 未收入金额
        expensesHaveBeenExpended: '17',// 已支出采购合同额
        unpaidPurchase: '27',//未支出采购合同额
        budgetSurplusKey: '19', // 预算结余
        grossProfitRateKey: '24', // 毛利率
        budgetKey: '28', // 费用预算
        purchaseContractKey: '29', // 采购签订合同额
        budgetExpensesKey: '30', // 预算费用支出
      }
      const operations = {
        // 12 立项阶段收入
        [key.incomeAtTheProjectApprovalStage]: () => {
          const totalAmount = this.findModelByPropValue(key.totalAmount)
          totalAmount.dataModel = this.inputNumFormat(dataModel);
          const unfinishedAmountKey = this.findModelByPropValue(key.unfinishedAmountKey);
          const theAmountThatHasBeenEarned = this.findModelByPropValue(key.theAmountThatHasBeenEarned)
          unfinishedAmountKey.dataModel = this.numSubtract(dataModel, theAmountThatHasBeenEarned.dataModel);

          // 毛利率计算 dataModel 是12
          const projectBudgetExpenditure = this.findModelByPropValue(key.projectBudgetExpenditure);
          const grossProfitRateKey = this.findModelByPropValue(key.grossProfitRateKey)
          grossProfitRateKey.dataModel = this.grossMarginCalculation(dataModel, projectBudgetExpenditure.dataModel);
        },
        // 15 已收入金额
        [key.theAmountThatHasBeenEarned]: () => {
          const unfinishedAmountKey = this.findModelByPropValue(key.unfinishedAmountKey);
          const incomeAtTheProjectApprovalStage = this.findModelByPropValue(key.incomeAtTheProjectApprovalStage);
          unfinishedAmountKey.dataModel = this.numSubtract(incomeAtTheProjectApprovalStage.dataModel, dataModel);
        },
        // 13 采购预算发生改变
        [key.projectBudgetExpenditure]: () => {
          const budgetSurplusKey = this.findModelByPropValue(key.budgetSurplusKey);

          // 毛利率计算
          const incomeAtTheProjectApprovalStage = this.findModelByPropValue(key.incomeAtTheProjectApprovalStage);
          const grossProfitRateKey = this.findModelByPropValue(key.grossProfitRateKey)
          grossProfitRateKey.dataModel = this.grossMarginCalculation(incomeAtTheProjectApprovalStage.dataModel, dataModel);

          // 预算结余计算
          const budgetExpensesKey = this.findModelByPropValue(key.budgetExpensesKey); // 预算费用支出 30
          const budgetKey = this.findModelByPropValue(key.budgetKey); // 费用预算28
          const purchaseContractKey = this.findModelByPropValue(key.purchaseContractKey); // 采购签订合同额 29
          let budget = this.numAdd(dataModel, budgetKey.dataModel);
          let expenditure = this.numAdd(purchaseContractKey.dataModel, budgetExpensesKey.dataModel);
          budgetSurplusKey.dataModel = this.numSubtract(budget, expenditure)

        },
        // 17 发生改变 已支出采购合同额
        [key.expensesHaveBeenExpended]: () => {
          // 采购签订合同额 = 已支出采购合同额+未支出采购合同额 29 = 17 + 27
          const purchaseContractKey = this.findModelByPropValue(key.purchaseContractKey); // 29
          const unpaidPurchase = this.findModelByPropValue(key.unpaidPurchase) // 27
          purchaseContractKey.dataModel = this.numAdd(dataModel, unpaidPurchase.dataModel);
        },
        // 27 发生改变 未支出采购合同额
        [key.unpaidPurchase]: () => {
          // 采购签订合同额 = 已支出采购合同额+未支出采购合同额 29 = 17 + 27
          const purchaseContractKey = this.findModelByPropValue(key.purchaseContractKey); // 29
          const expensesHaveBeenExpended = this.findModelByPropValue(key.expensesHaveBeenExpended) // 17
          purchaseContractKey.dataModel = this.numAdd(dataModel, expensesHaveBeenExpended.dataModel);
        },
        // 28 发生改变 费用预算
        [key.budgetKey]: () => {
          const budgetSurplusKey = this.findModelByPropValue(key.budgetSurplusKey) // 19 预算结余
          const projectBudgetExpenditure = this.findModelByPropValue(key.projectBudgetExpenditure); // 预算采购13
          const purchaseContractKey = this.findModelByPropValue(key.purchaseContractKey); // 费用预算28
          const budgetExpensesKey = this.findModelByPropValue(key.budgetExpensesKey); // 采购签订合同额 30
          let budget = this.numAdd(projectBudgetExpenditure.dataModel, dataModel);
          let expenditure = this.numAdd(purchaseContractKey.dataModel, budgetExpensesKey.dataModel);
          budgetSurplusKey.dataModel = this.numSubtract(budget, expenditure)
        },
        // 29 发生改变 采购签订合同额
        [key.purchaseContractKey]: () => {
          const budgetSurplusKey = this.findModelByPropValue(key.budgetSurplusKey) // 19 预算结余
          const projectBudgetExpenditure = this.findModelByPropValue(key.projectBudgetExpenditure); // 预算采购13
          const budgetKey = this.findModelByPropValue(key.budgetKey); // 费用预算28
          const budgetExpensesKey = this.findModelByPropValue(key.budgetExpensesKey); // 采购签订合同额 30
          let budget = this.numAdd(projectBudgetExpenditure.dataModel, budgetKey.dataModel);
          let expenditure = this.numAdd(dataModel, budgetExpensesKey.dataModel);
          budgetSurplusKey.dataModel = this.numSubtract(budget, expenditure)
        },
        // 30 发生改变 预算费用支出
        [key.budgetExpensesKey]: () => {
          const budgetSurplusKey = this.findModelByPropValue(key.budgetSurplusKey) // 30
          const projectBudgetExpenditure = this.findModelByPropValue(key.projectBudgetExpenditure); // 预算采购13
          const budgetKey = this.findModelByPropValue(key.budgetKey); // 费用预算28
          const purchaseContractKey = this.findModelByPropValue(key.purchaseContractKey); // 采购签订合同额 29
          let budget = this.numAdd(projectBudgetExpenditure.dataModel, budgetKey.dataModel);
          let expenditure = this.numAdd(dataModel, purchaseContractKey.dataModel);
          budgetSurplusKey.dataModel = this.numSubtract(budget, expenditure)
        }
      };
      const operation = operations[model];
      if (operation) {
        operation();
      } else {
        console.error('Invalid model');
      }
    },
    findModelByPropValue(propValue) {
      const children = this.$parent.$children;
      return children.find(item => item.propValue == propValue);
    },
    // 计算函数
    numSubtract(a, b) {
      const num1 = parseFloat(a) || 0.00;
      const num2 = parseFloat(b) || 0.00;
      let num = numeric.sub(num1, num2)
      return this.numformat(num);
    },
    numAdd(a, b) {
      const num1 = parseFloat(a) || 0.00;
      const num2 = parseFloat(b) || 0.00;
      let num = numeric.add(num1, num2)
      return this.numformat(num);
    },
    grossMarginCalculation(a, b) {
      const num1 = parseFloat(a) || 0.00;
      const num2 = parseFloat(b) || 0.00;
      if (num1 === 0) {
        return '0.00%'; // 避免除以0的情况
      }
      const c = numeric.sub(num1, num2);       // 相减
      const result = numeric.mul(numeric.div(c, num1), 100);
      // return `${ result.toFixed(2) }%`;
      return this.numformat(result) + '%';
    },
    numformat(num) {
      let str = num.toFixed(2).toString()
      // return str.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      return str
    },
    inputNumFormat(num) {
      const num1 = parseFloat(num) || 0.00
      return this.numformat(num1)
    }
  }
}
</script>

<style>
.previewEditorDiv>p {
  margin: 0;
}

.preview-cascader-class .el-input.is-disabled .el-input__inner {
  background-color: #fff;
  border: none;
  color: #303133;
}

.preview-cascader-class .el-input.is-disabled .el-input__suffix .el-input__suffix-inner .el-input__icon.el-icon-arrow-down:before {
  content: ''
}
</style>
