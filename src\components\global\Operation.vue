<!--搜索与重置-->
<template>
  <span>
    <el-button class="filter-item" size="mini" type="success" icon="el-icon-search" @click="handleCurrentChange()">搜索</el-button>
    <el-button v-if="crud.optShow.reset" class="filter-item" size="mini" type="warning" icon="el-icon-refresh-left" @click="resetQuery()">重置</el-button>
  </span>
</template>
<script>

export default {
  name: 'Operation',
  props: {

  },
  methods: {
    // 点击查询
    handleCurrentChange(e) {
      this.$emit('handleCurrentChange', e);
    },
    // 重置查询表单条件
    resetQuery() {

    }
  }
}
</script>
