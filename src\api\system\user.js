import request from '@/utils/request'
import { encrypt } from '@/utils/rsaEncrypt'

export function getUser(params) {
  return request({
    url: 'api/users',
    method: 'get',
    params
  })
}

export function getByName(params) {
  return request({
    url: 'api/users/findByNameWithBlur',
    method: 'get',
    params
  })
}

export function get(data) {
  return request({
    url: 'api/logs/user',
    method: 'get',
    data
  })
}

export function add(data) {
  return request({
    url: 'api/users',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/users',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/users',
    method: 'put',
    data
  })
}

export function editUser(data) {
  return request({
    url: 'api/users/center',
    method: 'put',
    data
  })
}

export function updatePass(user) {
  const data = {
    oldPass: encrypt(user.oldPass),
    newPass: encrypt(user.newPass)
  }
  return request({
    url: 'api/users/updatePass/',
    method: 'post',
    data
  })
}

export function updateEmail(form) {
  const data = {
    password: encrypt(form.pass),
    email: form.email
  }
  return request({
    url: 'api/users/updateEmail/' + form.code,
    method: 'post',
    data
  })
}

export function register(data) {
  return request({
    url: 'api/users/reg',
    method: 'post',
    data
  })
}

export function resetPasswd(data) {
  return request({
    url: 'api/users/resetPasswd',
    method: 'post',
    data
  })
}

export default { add, edit, del, getUser, register, resetPasswd, getByName }

