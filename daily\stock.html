<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>资产管理库存</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
<table>
    <thead>
    <tr>
        <th>资产编号</th>
        <th>库房名称</th>
        <th>库房ID</th>
        <th>项目名称</th>
        <th>项目ID</th>
        <th>设备名称</th>
        <th>设备ID</th>
        <th>品牌名称</th>
        <th>品牌ID</th>
        <th>型号名称</th>
        <th>型号ID</th>
        <th>入库数量</th>
        <th>库存数量</th>
        <th>出库数量</th>
        <th>盘库数量</th>
    </tr>
    </thead>
    <tbody>
    #for(x : amStock.queryAllSmall(mixQuery.amStock??, pageable??).content)
    <tr>
        <td string>#(x.basicData.basicNo??)</td>
        <td>#(x.basicData.depot.title??)</td>
        <td>#(x.basicData.depot.id??)</td>
        <td>#(x.basicData.pm.name??)</td>
        <td>#(x.basicData.pm.id??)</td>
        <td>#(x.basicData.device.name??)</td>
        <td>#(x.basicData.device.id??)</td>
        <td>#(x.basicData.brand.name??)</td>
        <td>#(x.basicData.brand.id??)</td>
        <td>#(x.basicData.model.name??)</td>
        <td>#(x.basicData.model.id??)</td>
        <td>#(x.stockInAmount??)</td>
        <td>#(x.stockAmount??)</td>
        <td>#(x.stockOutAmount??)</td>
        <td>#(x.stockCount??)</td>
    </tr>
    #end
    </tbody>
</table>
</body>
</html>
