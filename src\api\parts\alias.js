import request from '@/utils/request'
// import qs from 'qs'
// 别名管理
export function get(params) {
  return request({
    url: 'api/omAlias',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/omAlias',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/omAlias',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/omAlias',
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: 'api/omAlias/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'api/omAlias/update/relation',
    method: 'put',
    data
  })
}

export function updateAssetId(data) {
  return request({
    url: 'api/omAlias/update/assetId',
    method: 'put',
    data
  })
}

export default { get, add, edit, del, updateFormStruct, updateRelation, updateAssetId }
