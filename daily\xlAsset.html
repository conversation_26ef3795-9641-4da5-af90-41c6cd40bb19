<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>雪亮资产</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>

<script>
	// Function to merge table cells
	function mergeTableCells(table, columns) {
		for (let col of columns) {
			let lastValue = '';
			let rowspan = 1;
			const rows = table.rows;
			console.log(rows, '<===>', 'rows')

			for (let i = 1; i < rows.length; i++) {
				const cell = rows[i].cells[col];
				const cellValue = cell.innerText.trim();
				console.log(cellValue, '<===>', 'cellValue')
				console.log(lastValue, '<===>', 'lastValue')

				if (cellValue === lastValue) {
					rowspan++;
					cell.style.display = 'none';
					rows[i - rowspan + 1].cells[col].rowSpan = rowspan;
				} else {
					lastValue = cellValue;
					rowspan = 1;
				}
			}
		}
	}

	document.addEventListener('DOMContentLoaded', () => {
		const table = document.querySelector('table');
		mergeTableCells(table, [1, 2]); // Merge columns 1 and 2
	});
</script>

<table>
    <thead>
    <tr>
        <th>序号</th>
        <th>融合后点位</th>
        <th>立杆位置</th>
        <th>总点位数量</th>
        <th>摄像机类型</th>
        <th>类型对应数量</th>
        <th>设备厂商</th>
        <th>设备型号</th>
        <th>派出所</th>
        <th>产权单位</th>
        <th>融合后道路</th>
        <th>盘点情况</th>
        <th>经度</th>
        <th>纬度</th>
    </tr>
    </thead>
    <tbody>
    #for(x : omAsset.queryAll(mixQuery.asset??,pageable??).content)
    <tr>
        <td>#(x.extend.data['0']??)</td>
        <td>#(x.fv4??)</td>
        <td>#(x.title??)</td>
        <td>#(x.fv6??)</td>
        <td>#(x.fv5??)</td>
        <td>#(x.fv7??)</td>
        <td>#(x.fv10??)</td>
        <td>#(x.fv11??)</td>
        <td>#(x.fv8??)</td>
        <td>#(x.fv12??)</td>
        <td>#(x.fv9??)</td>
        <td>#(x.fv13??)</td>
        <td>#(x.fv2??)</td>
        <td>#(x.fv3??)</td>
    </tr>
    #end
    </tbody>
</table>

</body>
</html>
