import axios from 'axios'
// import { getToken } from '@/utils/auth'
const request = axios.create({
  withCredentials: false
})

request.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(new Error(error).message)
  }
)

request.interceptors.response.use(
  response => {
    console.log('xiangu');
    return response.data
  },
  error => {
    console.log('cuowu');
    return Promise.reject(new Error(error).message)
  }
)

export default request
