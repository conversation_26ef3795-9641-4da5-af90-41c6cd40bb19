import request from '@/utils/request'
// import qs from 'qs'
// 查询模板
export function get(params) {
  return request({
    url: 'api/extendOptionGlobal',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/extendOptionGlobal',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/extendOptionGlobal',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/extendOptionGlobal',
    method: 'put',
    data
  })
}

export function findOptions(params) {
  return request({
    url: 'api/extendOptionGlobal/options',
    method: 'get',
    params
  })
}
export function updateFormStruct(data) {
  return request({
    url: 'api/extendOptionGlobal/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'api/extendOptionGlobal/update/relation',
    method: 'put',
    data
  })
}

export default { get, add, edit, del, findOptions, updateFormStruct, updateRelation }
