<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://gtms04.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=896287" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d1;</span>
                <div class="name">clear</div>
                <div class="code-name">&amp;#xe6d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">代码生成</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">delete</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe630;</span>
                <div class="name">发布</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">清空回收站</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">code</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe713;</span>
                <div class="name">table</div>
                <div class="code-name">&amp;#xe713;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe91f;</span>
                <div class="name">diy-com-textarea</div>
                <div class="code-name">&amp;#xe91f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">自定义数据</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bd;</span>
                <div class="name">json</div>
                <div class="code-name">&amp;#xe7bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe695;</span>
                <div class="name">符号-级联选择</div>
                <div class="code-name">&amp;#xe695;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">icon_clone</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">文件上传</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66e;</span>
                <div class="name">分割线</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67d;</span>
                <div class="name">图片预览</div>
                <div class="code-name">&amp;#xe67d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">time</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">rate</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">ad-icon-tooltip</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">check-box</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">switch</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe640;</span>
                <div class="name">评分</div>
                <div class="code-name">&amp;#xe640;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b2;</span>
                <div class="name">input</div>
                <div class="code-name">&amp;#xe6b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">radio</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">color</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">slider</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c4;</span>
                <div class="name">icon_bars</div>
                <div class="code-name">&amp;#xe7c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe682;</span>
                <div class="name">富文本框</div>
                <div class="code-name">&amp;#xe682;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69f;</span>
                <div class="name">date</div>
                <div class="code-name">&amp;#xe69f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe621;</span>
                <div class="name">自定义字段</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">trash</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ad;</span>
                <div class="name">grid-45</div>
                <div class="code-name">&amp;#xe6ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe842;</span>
                <div class="name">drag</div>
                <div class="code-name">&amp;#xe842;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">上传</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63f;</span>
                <div class="name">栅格</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76b;</span>
                <div class="name">number</div>
                <div class="code-name">&amp;#xe76b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe668;</span>
                <div class="name">预览</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe920;</span>
                <div class="name">select</div>
                <div class="code-name">&amp;#xe920;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67c;</span>
                <div class="name">文字设置-22</div>
                <div class="code-name">&amp;#xe67c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">评分</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe620;</span>
                <div class="name">单栏布局</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">sync</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">tabs</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-clear"></span>
            <div class="name">
              clear
            </div>
            <div class="code-name">.icon-clear
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weibiaoti46"></span>
            <div class="name">
              代码生成
            </div>
            <div class="code-name">.icon-weibiaoti46
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-delete"></span>
            <div class="name">
              delete
            </div>
            <div class="code-name">.icon-delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fabu"></span>
            <div class="name">
              发布
            </div>
            <div class="code-name">.icon-fabu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingkonghuishouzhan"></span>
            <div class="name">
              清空回收站
            </div>
            <div class="code-name">.icon-qingkonghuishouzhan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-code"></span>
            <div class="name">
              code
            </div>
            <div class="code-name">.icon-code
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-table"></span>
            <div class="name">
              table
            </div>
            <div class="code-name">.icon-table
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-diy-com-textarea"></span>
            <div class="name">
              diy-com-textarea
            </div>
            <div class="code-name">.icon-diy-com-textarea
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zidingyishuju"></span>
            <div class="name">
              自定义数据
            </div>
            <div class="code-name">.icon-zidingyishuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-json"></span>
            <div class="name">
              json
            </div>
            <div class="code-name">.icon-json
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jilianxuanze"></span>
            <div class="name">
              符号-级联选择
            </div>
            <div class="code-name">.icon-jilianxuanze
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_clone"></span>
            <div class="name">
              icon_clone
            </div>
            <div class="code-name">.icon-icon_clone
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjianshangchuan"></span>
            <div class="name">
              文件上传
            </div>
            <div class="code-name">.icon-wenjianshangchuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fengexian"></span>
            <div class="name">
              分割线
            </div>
            <div class="code-name">.icon-fengexian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tupianyulan"></span>
            <div class="name">
              图片预览
            </div>
            <div class="code-name">.icon-tupianyulan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-time"></span>
            <div class="name">
              time
            </div>
            <div class="code-name">.icon-time
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon-test"></span>
            <div class="name">
              rate
            </div>
            <div class="code-name">.icon-icon-test
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tupian"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.icon-tupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ad-icon-tooltip"></span>
            <div class="name">
              ad-icon-tooltip
            </div>
            <div class="code-name">.icon-ad-icon-tooltip
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-check-box"></span>
            <div class="name">
              check-box
            </div>
            <div class="code-name">.icon-check-box
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-switch"></span>
            <div class="name">
              switch
            </div>
            <div class="code-name">.icon-switch
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pingfen"></span>
            <div class="name">
              评分
            </div>
            <div class="code-name">.icon-pingfen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-input"></span>
            <div class="name">
              input
            </div>
            <div class="code-name">.icon-input
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-radio-active"></span>
            <div class="name">
              radio
            </div>
            <div class="code-name">.icon-radio-active
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-color"></span>
            <div class="name">
              color
            </div>
            <div class="code-name">.icon-color
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-slider"></span>
            <div class="name">
              slider
            </div>
            <div class="code-name">.icon-slider
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_bars"></span>
            <div class="name">
              icon_bars
            </div>
            <div class="code-name">.icon-icon_bars
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuwenbenkuang"></span>
            <div class="name">
              富文本框
            </div>
            <div class="code-name">.icon-fuwenbenkuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-date"></span>
            <div class="name">
              date
            </div>
            <div class="code-name">.icon-date
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ic"></span>
            <div class="name">
              自定义字段
            </div>
            <div class="code-name">.icon-ic
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-trash"></span>
            <div class="name">
              trash
            </div>
            <div class="code-name">.icon-trash
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-grid-"></span>
            <div class="name">
              grid-45
            </div>
            <div class="code-name">.icon-grid-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-drag"></span>
            <div class="name">
              drag
            </div>
            <div class="code-name">.icon-drag
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangchuan"></span>
            <div class="name">
              上传
            </div>
            <div class="code-name">.icon-shangchuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhage"></span>
            <div class="name">
              栅格
            </div>
            <div class="code-name">.icon-zhage
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-number"></span>
            <div class="name">
              number
            </div>
            <div class="code-name">.icon-number
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yulan"></span>
            <div class="name">
              预览
            </div>
            <div class="code-name">.icon-yulan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-select"></span>
            <div class="name">
              select
            </div>
            <div class="code-name">.icon-select
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenzishezhi-"></span>
            <div class="name">
              文字设置-22
            </div>
            <div class="code-name">.icon-wenzishezhi-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pingfen1"></span>
            <div class="name">
              评分
            </div>
            <div class="code-name">.icon-pingfen1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-RectangleCopy"></span>
            <div class="name">
              单栏布局
            </div>
            <div class="code-name">.icon-RectangleCopy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sync1"></span>
            <div class="name">
              sync
            </div>
            <div class="code-name">.icon-sync1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tabs"></span>
            <div class="name">
              tabs
            </div>
            <div class="code-name">.icon-tabs
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3>第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-clear"></use>
                </svg>
                <div class="name">clear</div>
                <div class="code-name">#icon-clear</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weibiaoti46"></use>
                </svg>
                <div class="name">代码生成</div>
                <div class="code-name">#icon-weibiaoti46</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-delete"></use>
                </svg>
                <div class="name">delete</div>
                <div class="code-name">#icon-delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fabu"></use>
                </svg>
                <div class="name">发布</div>
                <div class="code-name">#icon-fabu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingkonghuishouzhan"></use>
                </svg>
                <div class="name">清空回收站</div>
                <div class="code-name">#icon-qingkonghuishouzhan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-code"></use>
                </svg>
                <div class="name">code</div>
                <div class="code-name">#icon-code</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-table"></use>
                </svg>
                <div class="name">table</div>
                <div class="code-name">#icon-table</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-diy-com-textarea"></use>
                </svg>
                <div class="name">diy-com-textarea</div>
                <div class="code-name">#icon-diy-com-textarea</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zidingyishuju"></use>
                </svg>
                <div class="name">自定义数据</div>
                <div class="code-name">#icon-zidingyishuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-json"></use>
                </svg>
                <div class="name">json</div>
                <div class="code-name">#icon-json</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jilianxuanze"></use>
                </svg>
                <div class="name">符号-级联选择</div>
                <div class="code-name">#icon-jilianxuanze</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_clone"></use>
                </svg>
                <div class="name">icon_clone</div>
                <div class="code-name">#icon-icon_clone</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjianshangchuan"></use>
                </svg>
                <div class="name">文件上传</div>
                <div class="code-name">#icon-wenjianshangchuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fengexian"></use>
                </svg>
                <div class="name">分割线</div>
                <div class="code-name">#icon-fengexian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tupianyulan"></use>
                </svg>
                <div class="name">图片预览</div>
                <div class="code-name">#icon-tupianyulan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-time"></use>
                </svg>
                <div class="name">time</div>
                <div class="code-name">#icon-time</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon-test"></use>
                </svg>
                <div class="name">rate</div>
                <div class="code-name">#icon-icon-test</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tupian"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#icon-tupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ad-icon-tooltip"></use>
                </svg>
                <div class="name">ad-icon-tooltip</div>
                <div class="code-name">#icon-ad-icon-tooltip</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-check-box"></use>
                </svg>
                <div class="name">check-box</div>
                <div class="code-name">#icon-check-box</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-switch"></use>
                </svg>
                <div class="name">switch</div>
                <div class="code-name">#icon-switch</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pingfen"></use>
                </svg>
                <div class="name">评分</div>
                <div class="code-name">#icon-pingfen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-input"></use>
                </svg>
                <div class="name">input</div>
                <div class="code-name">#icon-input</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-radio-active"></use>
                </svg>
                <div class="name">radio</div>
                <div class="code-name">#icon-radio-active</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-color"></use>
                </svg>
                <div class="name">color</div>
                <div class="code-name">#icon-color</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-slider"></use>
                </svg>
                <div class="name">slider</div>
                <div class="code-name">#icon-slider</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_bars"></use>
                </svg>
                <div class="name">icon_bars</div>
                <div class="code-name">#icon-icon_bars</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuwenbenkuang"></use>
                </svg>
                <div class="name">富文本框</div>
                <div class="code-name">#icon-fuwenbenkuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-date"></use>
                </svg>
                <div class="name">date</div>
                <div class="code-name">#icon-date</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ic"></use>
                </svg>
                <div class="name">自定义字段</div>
                <div class="code-name">#icon-ic</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-trash"></use>
                </svg>
                <div class="name">trash</div>
                <div class="code-name">#icon-trash</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-grid-"></use>
                </svg>
                <div class="name">grid-45</div>
                <div class="code-name">#icon-grid-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-drag"></use>
                </svg>
                <div class="name">drag</div>
                <div class="code-name">#icon-drag</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangchuan"></use>
                </svg>
                <div class="name">上传</div>
                <div class="code-name">#icon-shangchuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhage"></use>
                </svg>
                <div class="name">栅格</div>
                <div class="code-name">#icon-zhage</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-number"></use>
                </svg>
                <div class="name">number</div>
                <div class="code-name">#icon-number</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yulan"></use>
                </svg>
                <div class="name">预览</div>
                <div class="code-name">#icon-yulan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-select"></use>
                </svg>
                <div class="name">select</div>
                <div class="code-name">#icon-select</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenzishezhi-"></use>
                </svg>
                <div class="name">文字设置-22</div>
                <div class="code-name">#icon-wenzishezhi-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pingfen1"></use>
                </svg>
                <div class="name">评分</div>
                <div class="code-name">#icon-pingfen1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-RectangleCopy"></use>
                </svg>
                <div class="name">单栏布局</div>
                <div class="code-name">#icon-RectangleCopy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sync1"></use>
                </svg>
                <div class="name">sync</div>
                <div class="code-name">#icon-sync1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tabs"></use>
                </svg>
                <div class="name">tabs</div>
                <div class="code-name">#icon-tabs</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3>第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
