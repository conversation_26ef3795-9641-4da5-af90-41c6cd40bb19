<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>自动驾驶3.0项目</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
<table>
    <thead>
    <tr>
        <th>序号</th>
        <th>路口编号</th>
        <th>路口名称</th>
        <th>踏勘分组</th>
        <th>路口类型</th>
        <th>区域</th>
        <th>是否灯控路口</th>
        <th>备注</th>
        <th>是否已踏勘</th>
        <th>情况分析</th>
        <th>状态</th>
        <th>经度</th>
        <th>维度</th>
    </tr>
    </thead>
    <tbody>
    #for(x : omAsset.queryAll(mixQuery.asset??,pageable??).content)
    <tr>
        <td>#(x.extend.data['0']??)</td>
        <td>#(x.fv4??)</td>
        <td>#(x.title??)</td>
        <td>#(x.extend.data['3']??)</td>
        <td>#(x.extend.data['4']??)</td>
        <td>#(x.extend.data['5']??)</td>
        <td>#(x.extend.data['6']??)</td>
        <td>#(x.extend.data['7']??)</td>
        <td>#(x.extend.data['8']??)</td>
        <td>#(x.extend.data['9']??)</td>
        <td>#(x.extend.data['10']??)</td>
        <td>#(x.fv2??)</td>
        <td>#(x.fv3??)</td>
    </tr>
    #end
    </tbody>
</table>
</body>
</html>
