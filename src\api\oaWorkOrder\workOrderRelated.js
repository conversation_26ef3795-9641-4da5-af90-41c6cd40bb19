import request from '@/utils/request'
// 工单管理--与我相关
const COMMONURL = 'api/oaWorkOrderRelated'

export function getWorkOrderRelated(params) {
  return request({
    url: `${COMMONURL}`,
    method: 'get',
    params
  })
}

export function addWorkOrderRelated(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'post',
    data
  })
}

export function delWorkOrderRelated(ids) {
  return request({
    url: `${COMMONURL}`,
    method: 'delete',
    data: ids
  })
}

export function editWorkOrderRelated(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'put',
    data
  })
}
