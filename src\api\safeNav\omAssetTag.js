import request from '@/utils/request'
import qs from 'qs';
// 自动驾驶3.0-附件施工进度
const COMMONURL = 'api/omAssetTag'

export function get(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}?${query}`,
    method: 'get'
  })
}

export function findFilterItem(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}/findFilterItem?${query}`,
    method: 'get'
  })
}

export function getSmall(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}/small?${query}`,
    method: 'get'
  })
}

export function updateAssetAndTag(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}/updateAssetAndTag?${query}`,
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: `${COMMONURL}`,
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: `${COMMONURL}/update/formStruct`,
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: `${COMMONURL}/update/relation`,
    method: 'put',
    data
  })
}

export function importRule(data) {
  return request({
    url: `${COMMONURL}/import/xls/rule`,
    method: 'post',
    data
  })
}

export function empty(data) {
  return request({
    url: 'api/omAssetTag/empty',
    method: 'delete',
    data
  })
}

// 获取标签队列的消息数量
export function queueEmpty(params) {
  return request({
    url: `${COMMONURL}/queue/empty`,
    method: 'get'
  })
}

// 清空有问题的标签队列
export function queueSize(params) {
  return request({
    url: `${COMMONURL}/queue/size`,
    method: 'get'
  })
}

export default {
  get,
  getSmall,
  add,
  edit,
  del,
  updateFormStruct,
  updateRelation,
  importRule,
  updateAssetAndTag,
  findFilterItem,
  empty,
  queueEmpty,
  queueSize
}
