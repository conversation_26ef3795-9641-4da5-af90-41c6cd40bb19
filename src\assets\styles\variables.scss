// base color
$blue: #324157;
$light-blue: #3A71A8;
$red: #C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow: #FEC171;
$panGreen: #30B08F;

// sidebar
$menuText: #fff;
$menuActiveText: #409EFF;
$subMenuActiveText: #fff; // https://github.com/ElemeFE/element/issues/12951
$menuHoverText: #fff;
$subMenuFocusText: #409EFF;

$menuBg: #2476F8;
$menuHover: rgb(29, 94, 198);
$menuActive: #fff;

$subMenuBg: #2476F8;
$subMenuHover: rgb(29, 94, 198);
$subMenuActive: #fff;

$sideBarWidth: 252px;

// the :export directives is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  menuActive: $menuActive;
  subMenuActive: $subMenuActive;
  menuHoverText: $menuHoverText;
}
