import request from '@/utils/request'
import qs from 'qs'

export function get(params) {
  return request({
    url: 'spider/api/spiderLabel' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function getSmall(params) {
  return request({
    url: 'spider/api/spiderLabel/small' + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: 'spider/api/spiderLabel',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'spider/api/spiderLabel',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'spider/api/spiderLabel',
    method: 'put',
    data
  })
}

export function rewriteEdit(data) {
  return request({
    url: 'spider/api/spiderLabel/edit',
    method: 'post',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: 'spider/api/spiderLabel/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: 'spider/api/spiderLabel/update/relation',
    method: 'put',
    data
  })
}

export function importRule(data) {
  return request({
    url: 'spider/api/spiderLabel/import/xls/rule',
    method: 'post',
    data
  })
}

export default { get, add, edit, del, getSmall, rewriteEdit, updateFormStruct, updateRelation, importRule }
