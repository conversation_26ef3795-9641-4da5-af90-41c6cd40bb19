<template>
  <div class="crud-opts">
    <span class="crud-opts-left">
      <!--左侧插槽-->
      <slot v-if="crud.operToggle" name="left" />
      <template v-if="crud.operToggle">
        <el-button
          v-if="crud.optShow.add"
          v-permission="permission.add"
          class="filter-item"
          icon="el-icon-plus"
          size="mini"
          type="primary"
          @click="crud.toAdd"
        >
          新增
        </el-button>
        <el-button
          v-if="crud.optShow.edit"
          v-permission="permission.edit"
          :disabled="crud.selections.length !== 1"
          class="filter-item"
          icon="el-icon-edit"
          size="mini"
          type="success"
          @click="crud.toEdit(crud.selections[0])"
        >
          修改
        </el-button>
        <el-button
          v-if="crud.optShow.del"
          slot="reference"
          v-permission="permission.del"
          :disabled="crud.selections.length === 0"
          :loading="crud.delAllLoading"
          class="filter-item"
          icon="el-icon-delete"
          size="mini"
          type="danger"
          @click="toDelete(crud.selections)"
        >
          删除
        </el-button>
        <el-button
          v-if="crud.optShow.download"
          :disabled="!crud.data.length"
          :loading="crud.downloadLoading"
          class="filter-item"
          icon="el-icon-download"
          size="mini"
          type="warning"
          @click="crud.doExport"
        >导出</el-button>
      </template>
      <template v-else>
        <el-button
          v-if="crud.optShow.add && showFixed"
          v-permission="permission.add"
          class="filter-item"
          icon="el-icon-plus"
          size="mini"
          type="primary"
          @click="crud.toAdd"
        >
          新增
        </el-button>
      </template>
      <slot name="fixed" />
      <!--固定-->
      <!--右侧-->
      <slot v-if="crud.operToggle" name="right" />
    </span>
    <el-button-group v-if="crud.optShow.rightGroup" class="crud-opts-right">
      <el-button
        icon="el-icon-menu"
        size="mini"
        @click="toggleOper()"
      />
      <el-button
        icon="el-icon-search"
        size="mini"
        @click="toggleSearch()"
      />
      <el-button
        icon="el-icon-refresh"
        size="mini"
        @click="crud.refresh()"
      />
      <el-popover
        placement="bottom-end"
        popper-class="table-header-popper"
        trigger="click"
        width="200"
      >
        <el-button
          v-if="crud.optShow.rightGroup4"
          slot="reference"
          icon="el-icon-s-grid"
          size="mini"
        >
          <i
            aria-hidden="true"
            class="fa fa-caret-down"
          />
        </el-button>
        <el-checkbox
          v-model="allColumnsSelected"
          :indeterminate="allColumnsSelectedIndeterminate"
          @change="handleCheckAllChange"
        >
          全选
        </el-checkbox>
        <el-checkbox
          v-for="item in tableColumns"
          :key="item.property"
          v-model="item.visible"
          @change="handleCheckedTableColumnsChange(item)"
        >
          {{ item.label }}
        </el-checkbox>
      </el-popover>
      <slot name="iconButton" />
    </el-button-group>
  </div>
</template>
<script>
import CRUD, { crud } from '@crud/crud'

function sortWithRef(src, ref) {
  const result = Object.assign([], ref)
  let cursor = -1
  src.forEach(e => {
    const idx = result.indexOf(e)
    if (idx === -1) {
      cursor += 1
      result.splice(cursor, 0, e)
    } else {
      cursor = idx
    }
  })
  return result
}

export default {
  mixins: [crud()],
  props: {
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    },
    hiddenColumns: {
      type: Array,
      default: () => {
        return []
      }
    },
    ignoreColumns: {
      type: Array,
      default: () => {
        return []
      }
    },
    showFixed: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableColumns: [],
      allColumnsSelected: true,
      allColumnsSelectedIndeterminate: false,
      tableUnwatcher: null,
      // 忽略下次表格列变动
      ignoreNextTableColumnsChange: false
    }
  },
  watch: {
    'crud.props.table'() {
      this.updateTableColumns()
      this.tableColumns.forEach(column => {
        if (this.hiddenColumns.indexOf(column.property) !== -1) {
          column.visible = false
          this.updateColumnVisible(column)
        }
      })
    },
    'crud.props.table.store.states.columns'(val) {
      this.updateTableColumns()
    }
  },
  created() {
    this.crud.updateProp('searchToggle', true)
  },
  methods: {
    updateTableColumns() {
      if (!this.crud.optShow.rightGroup4) {
        return;
      }
      const table = this.crud.getTable()
      if (!table) {
        this.tableColumns = []
        return
      }
      let cols = null
      const columnFilter = e => e && e.type === 'default' && e.property && this.ignoreColumns.indexOf(e.property) === -1
      const refCols = table.columns.filter(columnFilter)
      if (this.ignoreNextTableColumnsChange) {
        this.ignoreNextTableColumnsChange = false
        return
      }
      this.ignoreNextTableColumnsChange = false
      const columns = []
      const fullTableColumns = table.$children.map(e => e.columnConfig).filter(columnFilter)
      cols = sortWithRef(fullTableColumns, refCols)
      cols.forEach(config => {
        const column = {
          property: config.property,
          label: config.label,
          visible: refCols.indexOf(config) !== -1
        }
        columns.push(column)
      })
      // if (columns.length > 8) {
      //   this.allColumnsSelected = false;
      //   //n this.ignoreNextTableColumnsChange = true
      //   for (let i = 8; i < columns.length; i++) {
      //     columns[i].visible = false;
      //   }
      //   this.tableColumns = columns
      // } else {
      //   this.tableColumns = columns
      // }
      this.tableColumns = columns
    },
    toDelete(datas) {
      this.$confirm(`确认删除选中的${datas.length}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.crud.delAllLoading = true
        this.crud.doDelete(datas)
      }).catch(() => {
      })
    },
    handleCheckAllChange(val) {
      if (val === false) {
        this.allColumnsSelected = true
        return
      }
      this.tableColumns.forEach(column => {
        if (!column.visible) {
          column.visible = true
          this.updateColumnVisible(column)
        }
      })
      this.allColumnsSelected = val
      this.allColumnsSelectedIndeterminate = false
    },
    handleCheckedTableColumnsChange(item) {
      let totalCount = 0
      let selectedCount = 0
      this.tableColumns.forEach(column => {
        ++totalCount
        selectedCount += column.visible ? 1 : 0
      })
      if (selectedCount === 0) {
        this.crud.notify('请至少选择一列', CRUD.NOTIFICATION_TYPE.WARNING)
        this.$nextTick(function() {
          item.visible = true
        })
        return
      }
      this.allColumnsSelected = selectedCount === totalCount
      this.allColumnsSelectedIndeterminate = selectedCount !== totalCount && selectedCount !== 0
      this.updateColumnVisible(item)
    },
    updateColumnVisible(item) {
      const table = this.crud.props.table
      const vm = table.$children.find(e => e.prop === item.property)
      const columnConfig = vm.columnConfig
      if (item.visible) {
        // 找出合适的插入点
        const columnIndex = this.tableColumns.indexOf(item)
        vm.owner.store.commit('insertColumn', columnConfig, columnIndex + 1, null)
      } else {
        vm.owner.store.commit('removeColumn', columnConfig, null)
      }
      this.ignoreNextTableColumnsChange = true
    },
    toggleSearch() {
      this.crud.props.searchToggle = !this.crud.props.searchToggle
    },
    toggleOper() {
      this.crud.operToggle = !this.crud.operToggle
    }
  }
}
</script>

<style>
.table-header-popper {
	max-height: 400px;
	overflow: auto;
	display: flex;
	flex-direction: column;
	top: 200px !important;
	right: 0 !important;
}

.crud-opts {
	padding: 4px 0;
	display: -webkit-flex;
	display: flex;
	/* align-items: center; */
  justify-content: space-between;
  align-items: flex-start;
}

.crud-opts .crud-opts-right {
	margin-left: auto;
  min-width:170px;
}

.crud-opts .crud-opts-right span {
	float: left;
}
</style>
