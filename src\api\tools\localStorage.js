import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/localStorage',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/localStorage/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/localStorage',
    method: 'put',
    data
  })
}

export function downloadFile(data) {
  const url = '/api/localStorage/download/file';
  return request({
    url: url,
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function downloadZip(data) {
  const url = '/api/localStorage/download/zip';
  return request({
    url: url,
    method: 'post',
    responseType: 'blob',
    data
  })
}

export default { add, edit, del, downloadFile, downloadZip }
