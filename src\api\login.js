import request from '@/utils/request'
import settings from '../settings'

export function login(username, password, code, uuid) {
  return request({
    url: 'auth/login',
    method: 'post',
    data: {
      username,
      password,
      code,
      uuid
    }
  })
}

export function getInfo() {
  return request({
    url: 'auth/info',
    method: 'get'
  })
}

export function getCodeImg() {
  return request({
    url: 'auth/code',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: 'auth/logout',
    method: 'delete'
  })
}

export function getAutherUrl() {
  return request({
    url: `wx/oauth2/${settings.appid}/authorizationUrl`,
    method: 'get'
  })
}

export function loginCode(params) {
  return request({
    url: `wx/oauth2/${settings.appid}/login`,
    method: 'get',
    params
  })
}
