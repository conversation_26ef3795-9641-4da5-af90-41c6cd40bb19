import request from '@/utils/request'
import qs from 'qs';
// 资产管理-仓库管理
const COMMONURL = 'api/amOrder'

export function get(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}?${query}`,
    method: 'get'
  })
}

export function getSmall(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}/small?${query}`,
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: `${COMMONURL}`,
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: `${COMMONURL}/update/formStruct`,
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: `${COMMONURL}/update/relation`,
    method: 'put',
    data
  })
}

export function importRule(data) {
  return request({
    url: `${COMMONURL}/import/xls/rule`,
    method: 'post',
    data
  })
}

export function stockIn(data) {
  return request({
    url: `${COMMONURL}/stockIn`,
    method: 'post',
    data
  })
}
export function importStockIn(data) {
  return request({
    url: `${COMMONURL}/importStockIn`,
    method: 'post',
    data
  })
}

export default {
  get,
  getSmall,
  add,
  edit,
  del,
  stockIn,
  updateFormStruct,
  updateRelation,
  importRule
}
