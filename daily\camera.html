<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">

<html>
<head>
	<meta charset="UTF-8">
	<meta http-equiv="content-type" content="text/html; charset=iso-8859-1"/>
	<title>摄像头信息</title>
	<meta name="generator" content="LibreOffice 5.4.7.2 (Linux)"/>
	<meta name="author" content="Microsoft Office User"/>
	<meta name="created" content="2021-06-04T01:30:47"/>
	<meta name="changedby" content="admin"/>
	<meta name="changed" content="2022-10-10T05:36:37"/>
	<meta name="AppVersion" content="16.0300"/>
	<meta name="DocSecurity" content="0"/>
	<meta name="HyperlinksChanged" content="false"/>
	<meta name="LinksUpToDate" content="false"/>
	<meta name="ScaleCrop" content="false"/>
	<meta name="ShareDoc" content="false"/>
	
	<style type="text/css">
		body,div,table,thead,tbody,tfoot,tr,th,td,p { font-family:"&#31561;&#32447;"; font-size:small }
		a.comment-indicator:hover + comment { background:#ffd; position:absolute; display:block; border:1px solid black; padding:0.5em;  } 
		a.comment-indicator { background:red; display:inline-block; border:1px solid black; width:0.5em; height:0.5em;  } 
		comment { display:none;  } 
    .td-t2blr {
      border-top: 2px solid #000000; 
      border-bottom: 1px solid #000000; 
      border-left: 1px solid #000000; 
      border-right: 1px solid #000000;
    }
    .td-tblr {
      border-top: 1px solid #000000; 
      border-bottom: 1px solid #000000; 
      border-left: 1px solid #000000; 
      border-right: 1px solid #000000;
    }
	</style>
	
</head>

<body>
<table cellspacing="0" border="0">
	<colgroup span="18" width="112"></colgroup>
	<tr>
		<td class="td-t2blr" height="48" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#36164;&#20135;&#32534;&#21495;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#30417;&#25511;&#28857;&#26032;&#21517;&#31216;&#65288;&#28857;&#20301;&#21517;&#31216;&#65289;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#30417;&#25511;&#28857;&#21517;&#31216;&#65288;&#28857;&#20301;&#21517;&#31216;&#65289;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#30417;&#25511;&#28857;&#32534;&#21495;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#23433;&#35013;&#20301;&#32622;66</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#26438;&#20301;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#30417;&#35270;&#26041;&#20301;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#25668;&#20687;&#26426;&#31867;&#22411;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">IP&#22320;&#22336;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#31471;&#21475;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#35774;&#22791;&#21378;&#21830;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#35774;&#22791;&#22411;&#21495;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#29992;&#25143;&#21517;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#23494;&#30721;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#32852;&#31995;&#20154;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#32852;&#31995;&#26041;&#24335;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#26435;&#37325;</font></b></td>
		<td class="td-t2blr" align="center" valign=middle bgcolor="#CCFFCC"><b><font face="DejaVu Sans Mono" size=2 color="#000000">&#22791;&#27880;9999</font></b></td>
	</tr>
  #for(x : omAssetDetail.queryAll(mixQuery.detail??,pageable??).content)
	<tr>
		<td class="td-tblr" height="48" align="left" valign=middle bgcolor="#CCFFFF"><font face="&#23435;&#20307;" size=2 color="#000000">#(x.extend.data['0']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['1']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['2']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="&#23435;&#20307;" size=2 color="#000000">#(x.extend.data['3']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['4']??)</font></td>
		<td class="td-tblr" align="left" valign=middle><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['5']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['6']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['7']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="&#23435;&#20307;" size=2 color="#000000">#(x.extend.data['8']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF" sdval="80" sdnum="1033;"><font face="&#23435;&#20307;" size=2 color="#000000">#(x.extend.data['9']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['10']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="&#23435;&#20307;" size=2 color="#000000">#(x.extend.data['11']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="&#23435;&#20307;" size=2 color="#000000">#(x.extend.data['12']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="&#23435;&#20307;" size=2 color="#000000">#(x.extend.data['13']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="DejaVu Sans Mono" size=2 color="#000000">#(x.extend.data['14']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF" sdval="15711382550" sdnum="1033;"><font face="&#23435;&#20307;" size=2 color="#000000">#(x.extend.data['15']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF" sdval="2" sdnum="1033;"><font face="&#23435;&#20307;" size=2 color="#000000">#(x.extend.data['16']??)</font></td>
		<td class="td-tblr" align="left" valign=middle bgcolor="#CCFFFF"><font face="&#23435;&#20307;" size=2 color="#000000"><br>#(x.extend.data['17']??)</font></td>
	</tr>
  #end
</table>
<!-- ************************************************************************** -->
</body>

</html>
