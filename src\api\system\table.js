import request from '@/utils/request'
// import qs from 'qs'
// 查询模板
export function get(params) {
  return request({
    url: 'api/extendSampleCase',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/extendSampleCase',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/extendSampleCase',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/extendSampleCase',
    method: 'put',
    data
  })
}

export function updateFormStruct(data) {
  return request({
    url: '/api/extendSampleCase/update/formStruct',
    method: 'put',
    data
  })
}

export function updateRelation(data) {
  return request({
    url: '/api/extendSampleCase/update/relation',
    method: 'put',
    data
  })
}

export default { get, add, edit, del, updateFormStruct, updateRelation }
