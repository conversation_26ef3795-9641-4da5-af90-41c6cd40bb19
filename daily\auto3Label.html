<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>自动驾驶-路口标签</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
<table>
    <thead>
    <tr>
        <th>顶级</th>
        <th>标签</th>
        <th>路口名称</th>
        <th>路口编号</th>
    </tr>
    </thead>
    <tbody>
    #for(x : omAsset.queryAll(mixQuery.asset??,pageable??).content)
    <tr>
        <td>#(x.fv??)</td>
        <td>#(x.fv4??)</td>
        <td>#(x.title??)</td>
        <td>#(x.fv6??)</td>
    </tr>
    #end
    </tbody>
</table>
</body>
</html>
