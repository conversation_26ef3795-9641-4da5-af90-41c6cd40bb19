// 工单接口
import request from '@/utils/request'
// import qs from 'qs'
// 查询OA-工作流-工单管理
export function getWorkOrderInfo(params) {
  return request({
    url: 'api/oaWorkOrderInfo',
    method: 'get',
    params
  })
}
// 修改OA-工作流-工单管理
export function editWorkOrderInfo(data) {
  return request({
    url: 'api/oaWorkOrderInfo',
    method: 'put',
    data
  })
}
// 导出数据
export function downloadWorkOrder(params) {
  return request({
    url: 'api/oaWorkOrderInfo/download',
    method: 'get',
    params
  })
}
// 工作流-导出工单
export function exportWorkOrder(params) {
  return request({
    url: 'api/oaWorkOrderInfo/export',
    method: 'get',
    params
  })
}
// 工作流-工单管理(步骤一)
export function workOrderFirst(params) {
  return request({
    url: 'api/oaWorkOrderInfo/process',
    method: 'get',
    params
  })
}
// 新增OA-工作流-工单管理
export function addWorkOrderInfo(data) {
  return request({
    url: 'api/oaWorkOrderInfo',
    method: 'post',
    data
  })
}
// 撤销OA-工作流-工单管理
export function cancelWorkOrder(data) {
  return request({
    url: 'api/oaWorkOrderInfo/cancel',
    method: 'post',
    data
  })
}
// 结单OA-工作流-工单管理
export function endWorkOrder(data) {
  return request({
    url: 'api/oaWorkOrderInfo/end',
    method: 'post',
    data
  })
}
// 转交OA-工作流-工单管理
export function forwardWorkOrder(data) {
  return request({
    url: 'api/oaWorkOrderInfo/forward',
    method: 'post',
    data
  })
}
// 审批OA-工作流-工单管理
export function handleWorkOrder(data) {
  return request({
    url: 'api/oaWorkOrderInfo/handle',
    method: 'post',
    data
  })
}
export function delWorkOrderInfo(ids) {
  return request({
    url: 'api/oaWorkOrderInfo',
    method: 'delete',
    data: ids
  })
}
