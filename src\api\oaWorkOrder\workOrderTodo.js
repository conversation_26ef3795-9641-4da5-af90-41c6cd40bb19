import request from '@/utils/request'
import qs from 'qs'
// 工单管理--待办任务
const COMMONURL = 'api/oaWorkOrderTodo'

export function getoaWorkOrderTodo(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}?${query}`,
    method: 'get',
    params
  })
}

export function addoaWorkOrderTodo(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'post',
    data
  })
}

export function deloaWorkOrderTodo(ids) {
  return request({
    url: `${COMMONURL}`,
    method: 'delete',
    data: ids
  })
}

export function editoaWorkOrderTodo(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'put',
    data
  })
}
