<template>
  <div>
    <el-upload
      ref="upload"
      :auto-upload="true"
      :before-remove="beforeRemove"
      :before-upload="beforeUpload"
      :class="isAddFolderFile ? 'isAddFolderFile' : ''"
      :disabled="element.options.disabled"
      :file-list="dataModel"
      :headers="headers"
      :http-request="uploadToAliyunOSS"
      :limit="element.options.length"
      :on-change="handleChange"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :style="{'width': element.options.width}"
      action=""
      multiple
    >
      <!-- 默认插槽，用于自定义文件列表的显示 -->
      <template slot="file" slot-scope="{ file }">
        <div style="display: flex; align-items: center; justify-content: space-between; width: 100%; margin-bottom: 5px;">
          <span style="cursor:pointer;" @click="openFile(file)">{{ file.name }}</span>
          <div style="display: flex; align-items: center;">
            <el-button
              v-if="uploadProgress[file.uid] === 100"
              size="mini"
              type="text"
              @click="handleRemoveFile(file)"
            >
              删除
            </el-button>
            <span v-else style="margin-right: 35px; color: #909399;">
              正在上传中...
            </span>
          </div>
        </div>
        <el-progress
          v-if="uploadProgress[file.uid] !== undefined && uploadProgress[file.uid] < 100"
          :percentage="uploadProgress[file.uid]"
        />
      </template>
      <div v-if="!preview && setWebkitdirectory">
        <el-button size="small" type="primary">点击上传</el-button>
        <div slot="tip" class="el-upload__tip">{{ element.options.tip }}</div>
      </div>
    </el-upload>
  </div>
</template>

<script>
import { downloadFile } from '@/utils/index'
import crudLocalStorage from '@/api/tools/localStorage'
import { getToken } from '@/utils/auth'
import { initOss, aliyunConfig } from './initOss'
import cloudStorage from '@/api/tools/cloudStorage';
import { mapGetters } from 'vuex';

export default {
  name: 'OssFile',
  props: ['element', 'preview', 'dataModel'],
  data() {
    return {
      fileListTmp: [],
      name: '',
      headers: { 'Authorization': getToken() },
      fullscreenLoading: false,
      uploading: false,
      ossClient: null,
      isAddFolderFile: false,
      setWebkitdirectory: false,
      uploadProgress: {} // 为对象，存储每个文件的上传进度
    }
  },
  watch: {
    'dataModel': {
      deep: true,
      immediate: true,
      handler(val) {
        if (val && val.length) {
          val.map(item => {
            this.$set(this.uploadProgress, item.uid, 100);
            if (item.filename && !item.name) {
              item.name = item.filename;
            }
          });

          this.fileListTmp = this.dataModel;
        }
      }
    }
  },
  computed: {
    ...mapGetters([
      'user',
      'preViewUrl'
    ])
  },
  async created() {
    const ossClient = await initOss();
    this.ossClient = ossClient;
    await this.$nextTick()
    const { isAddFolderFile } = this.element.options;
    this.isAddFolderFile = isAddFolderFile;
    setTimeout(() => {
      const elements = document.querySelectorAll('.isAddFolderFile .el-upload__input');
      elements.forEach((element) => {
        element.webkitdirectory = true;
      });
      this.setWebkitdirectory = true;
    }, 20)
  },
  methods: {
    openFile(file) {
      const url = file.raw.response.url;
      window.open(`${this.preViewUrl}/onlinePreview?url=${encodeURIComponent(btoa(url))}`);
    },
    async uploadToAliyunOSS(initFile) {
      this.$store.dispatch('formMaking/setIsAllUpload', false)
      const { file } = initFile;
      const { uid, name } = file; // 获取文件的唯一标识符
      const { fileName, timestamp, ext } = this.generateObjectName(file);
      file.fileName = fileName;
      file.timestamp = timestamp;
      file.ext = ext;
      file.fileTypes = this.element.type
      const FilesSize = 104857600

      let result;
      try {
        if (file.size > FilesSize) {
          // 分片上传配置选项
          const options = {
            parallel: 4, // 同时上传的并发分片数量
            partSize: 20 * 1024 * 1024, // 每个分片大小为20MB
            progress: (percentage, checkpoint) => {
              this.$set(this.uploadProgress, uid, Math.round(percentage * 100)); // 设置每个文件的上传进度
            }
          };
          // 分片上传并完成操作
          await this.ossClient.multipartUpload(fileName, file, options);
          // 如果Bucket允许公共读取，构建公开访问URL
          result = {
            url: `${aliyunConfig.domain}${fileName}`
          }
        } else {
          // 直接上传文件
          result = await this.ossClient.put(fileName, file);
          this.$set(this.uploadProgress, uid, 100); // 上传完成后设置进度为100%
        }
      } catch (err) {
        this.$notify({
          type: 'error',
          title: `${name} 上传失败,请重新上传`,
          duration: 0
        })
      }

      // 处理文件上传结果
      const currentFile = this.hanleFileInfo(file, result);
      file.response = currentFile;
      // 调用后续处理方法
      this.uploadSelfFlatbed(currentFile);
    },
    handleRemove(file, fileList) {
      this.fileListTmp = fileList
      this.$emit('fileList', fileList)
    },
    handlePreview(file) {
      const loading = this.$loading({
        lock: true,
        text: '下载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      if (file?.platform.indexOf('local') != -1) {
        crudLocalStorage.downloadFile(file.response).then((res) => {
          loading.close();
          downloadFile(res, file.name, file.type)
        }).catch(e => {
          loading.close();
        });
      } else {
        window.open(file.url)
        loading.close();
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(`最多允许上传 ${this.element.options.length} 个文件。`)
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定要移除 ${file.name}？`)
    },
    beforeUpload(file) {
      return true
    },
    handleChange(fileList) {
      // console.log(fileList, '<===>', 'fileList')
    },
    handleSuccess(response, file, fileList) {
      // this.$delete(this.uploadProgress, file.uid); // 上传成功后，移除进度条
      const allFilesUploaded = fileList.every(file => file.status === 'success');

      if (allFilesUploaded) {
        // this.fullscreenLoading = false
        this.$store.dispatch('formMaking/setIsAllUpload', true)
        // 执行你的操作
        this.$notify({
          title: '文件上传成功',
          type: 'success',
          duration: 0
        })
      }
      this.fileListTmp = fileList
      this.$emit('fileList', this.fileListTmp)
    },
    handleRemoveFile(file) {
      this.$confirm(`确定要移除 ${file.name}？`)
        .then(() => {
          this.fileListTmp = this.fileListTmp.filter(f => f.uid !== file.uid);
          this.$emit('fileList', this.fileListTmp);
        })
        .catch(() => {
          // 取消删除
        });
    },
    generateObjectName(file) {
      const { name } = file;
      // 获取文件名和扩展名
      const lastDotIndex = name.lastIndexOf('.');
      const nameWithoutExt = lastDotIndex > 0 ? name.substring(0, lastDotIndex) : name;
      const ext = lastDotIndex > 0 ? name.substring(lastDotIndex + 1) : '';

      // 生成时间戳
      const timestamp = Math.floor(Date.now() / 1000);

      // 构建新的文件名格式
      const newFileName = `${nameWithoutExt}_${timestamp}_.${ext}`;

      if (this.element.options.customPath) {
        // 有自定义路径的情况
        let fullPath = this.element.options.customPath;

        // 如果是文件夹上传,需要拼接文件夹路径
        if (this.isAddFolderFile && file.webkitRelativePath) {
          // 获取文件夹路径(去掉文件名)
          const folderPath = file.webkitRelativePath.split('/').slice(0, -1).join('/');
          if (folderPath) {
            fullPath = `${fullPath}/${folderPath}`;
          }
        }

        return {
          fileName: `asset/${fullPath}/${newFileName}`,
          timestamp: timestamp,
          ext: ext
        };
      } else {
        // 没有自定义路径,使用年月日格式
        const dateFormat = this.$dayJS().format('YYYY/MM/DD');
        return {
          fileName: `asset/${dateFormat}/${newFileName}`,
          timestamp: timestamp,
          ext: ext
        };
      }
    },
    // 上传到自己的平台
    uploadSelfFlatbed(fileInfo) {
      cloudStorage.create(fileInfo).then(res => {
      })
    },

    hanleFileInfo(file, result) {
      const { name, size, ext, webkitRelativePath, fileName } = file;
      const { url } = result;

      // 构建基本路径
      const basePath = 'asset/';
      let filePath;

      if (this.element.options.customPath) {
        // 使用自定义路径
        if (this.isAddFolderFile && webkitRelativePath) {
          // 获取文件夹路径(去掉文件名)
          const folderPath = webkitRelativePath.split('/').slice(0, -1).join('/');
          filePath = folderPath
            ? `${this.element.options.customPath}/${folderPath}`
            : this.element.options.customPath;
        } else {
          filePath = this.element.options.customPath;
        }
      } else {
        // 使用日期格式路径
        filePath = this.$dayJS().format('YYYY/MM/DD');
      }

      // 创建基础查询对象
      const query = {
        basePath: basePath,
        enabled: 1,
        ext: ext,
        filename: name, // 保留原始文件名
        name: name,
        originalFilename: name,
        path: filePath, // 使用包含文件夹路径的完整路径
        platform: 'ALIYUN-oss-1',
        size: size,
        url: url,
        createBy: this.user.username,
        ossPath: fileName // 添加完整的OSS路径
      }

      // 如果是文件夹上传，添加相对路径
      if (this.isAddFolderFile) {
        query['webkitRelativePath'] = webkitRelativePath;
      }

      return query;
    }
  }
}
</script>

<style scoped>

</style>
