<template>
  <div class="thumbnail-view">
    <el-image
      v-if="url && url!==''"
      :preview-src-list="previewList"
      :src="url"
      class="el-avatar"
      fit="contain"
      lazy
      v-bind="$attrs"
    >
      <div slot="error">
        <i class="el-icon-document" />
      </div>
    </el-image>
    <div v-else class="el-avatar">
      <img
        v-if="findFileTypeByExtension()"
        :src="require(`@/assets/file_images/${findFileTypeByExtension()}.png`)"
        alt=""
        @click="preView()"
      >
      <i v-else class="el-icon-document " @click="preView()" />
    </div>

  </div>
</template>

<script>
export default {
  name: 'FileThumb',
  props: {
    url: {
      type: String,
      default: ''
    },
    previewList: {
      type: Array,
      default: () => []
    },
    fileExt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileType: {
        excel: ['xlsx', 'xls'],
        word: ['doc', 'docx'],
        ppt: ['ppt', 'pptx'],
        pdf: ['pdf']
      }
    }
  },
  computed: {
    findFileTypeByExtension({ fileType, fileExt }) {
      return () => {
        if (!fileExt) {
          return '';
        }
        for (const key in fileType) {
          if (fileType[key].includes(fileExt.toLowerCase())) {
            return key;
          }
        }
        return ''; // 如果没有找到匹配项，则返回默认值
      }
    }
  },
  methods: {
    preView() {
      this.$emit('preView', this.previewList[0])
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
</style>
