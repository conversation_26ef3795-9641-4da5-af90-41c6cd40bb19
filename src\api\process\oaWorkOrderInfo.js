import request from '@/utils/request'
import qs from 'qs'
export function get(data) {
  const url = 'api/oaWorkOrderInfo';
  return request({
    url: url + '?' + qs.stringify(data, { indices: false }),
    method: 'get'
  })
}

export function processStructure(data) {
  const url = 'api/oaWorkOrderInfo/process';
  return request({
    url: url + '?' + qs.stringify(data, { indices: false }),
    method: 'get'
  })
}
export function add(data) {
  return request({
    url: 'api/oaWorkOrderInfo',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/oaWorkOrderInfo',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/oaWorkOrderInfo',
    method: 'put',
    data
  })
}
// 接单
export function end(data) {
  return request({
    url: 'api/oaWorkOrderInfo/end',
    method: 'post',
    data
  })
}
// 撤销
export function cancelWorkOrder(data) {
  return request({
    url: '/api/oaWorkOrderInfo/cancel',
    method: 'post',
    data
  })
}

// 导出议题
export function exportProcess(data) {
  const url = 'api/oaWorkOrderInfo/export';
  return request({
    url: url + '?' + qs.stringify(data, { indices: false }),
    method: 'get'
  })
}

export default { get, add, edit, del, end, cancelWorkOrder, exportProcess }
