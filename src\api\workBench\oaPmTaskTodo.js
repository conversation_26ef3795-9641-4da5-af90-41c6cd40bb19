import request from '@/utils/request'
import qs from 'qs'
// 待办任务

const COMMONURL = 'api/oaPmTaskTodo'

export function getTaskTodo(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}?${query}`,
    method: 'get'
  })
}

export function getTodoByPmName(params) {
  const query = qs.stringify(params, { indices: false });
  return request({
    url: `${COMMONURL}/countGroupByPmName?${query}`,
    method: 'get'
  })
}

export function addTaskTodo(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'post',
    data
  })
}

export function delTaskTodo(ids) {
  return request({
    url: `${COMMONURL}`,
    method: 'delete',
    data: ids
  })
}

export function editTaskTodo(data) {
  return request({
    url: `${COMMONURL}`,
    method: 'put',
    data
  })
}
