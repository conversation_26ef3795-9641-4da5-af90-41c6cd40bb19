.head-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin: 0 3px 10px 0;

    input {
      height: 30.5px;
      line-height: 30.5px;
    }
  }

  .el-form-item-label {
    margin: 0 3px 9px 0;
    display: inline-block;
    text-align: right;
    vertical-align: middle;
    font-size: 14px;
    color: #606266;
    line-height: 30.5px;
    padding: 0 7px 0 7px;
  }

  .el-button + .el-button {
    margin-left: 0 !important;
  }

  .el-select__caret.el-input__icon.el-icon-arrow-up {
    line-height: 30.5px;
  }

  .date-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
    height: 30.5px !important;
    width: 230px !important;
  }
}

.el-avatar {
  display: inline-block;
  text-align: center;
  background: #ccc;
  color: #fff;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  vertical-align: middle;
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: 16px;
}

.logo-con {
  height: 60px;
  padding: 13px 0 0;

  img {
    height: 32px;
    width: 135px;
    display: block;
    //margin: 0 auto;
  }
}

#el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial, serif;
  font-size: 12px;
  letter-spacing: 1px;
}

#el-main-footer {
  background: none repeat scroll 0 0 white;
  border-top: 1px solid #e7eaec;
  overflow: hidden;
  padding: 10px 6px 0 6px;
  height: 33px;
  font-size: 0.7rem !important;
  color: #7a8b9a;
  letter-spacing: 0.8px;
  font-family: Arial, sans-serif !important;
  position: fixed;
  bottom: 0;
  z-index: 99;
  width: 100%;
}

.eladmin-upload {
  border: 1px dashed #c0ccda;
  border-radius: 5px;
  height: 45px;
  line-height: 45px;
  width: 368px;
}

.my-blockquote {
  margin: 0 0 10px;
  padding: 15px;
  line-height: 22px;
  border-left: 5px solid #00437B;
  border-radius: 0 2px 2px 0;
  background-color: #f2f2f2;
}

.my-code {
  position: relative;
  padding: 15px;
  line-height: 20px;
  border-left: 5px solid #ddd;
  color: #333;
  font-family: Courier New, serif;
  font-size: 12px
}

.el-tabs {
  margin-bottom: 25px;
}

// 表格统一样式

.el-table table {
  width: 100% !important;
}

.el-table__header, .el-table__body, .el-table__footer {
  width: 100% !important;
  table-layout: fixed !important;
}

.el-table__fixed-right {
  height: 100% !important;
}

body .el-table th.gutter {
  display: table-cell !important;
}

body .el-table colgroup.gutter {
  display: table-cell !important;
}

table {
  width: 100% !important;
}

.el-table thead {
  width: 100%;

  tr {
    height: 24px;

    th.el-table__cell {
      background-color: #FFF;
      background: #F6F8FA;
      color: #364359;
      line-height: 16px;
      font-weight: normal;
    }
  }
}

.el-table tbody {
  width: 100%;

  tr {
    height: 44px !important;
    box-sizing: border-box;
    line-height: 44px;

    td {
      height: 44px !important;
      line-height: 44px;
      padding: 2px 0 !important;
      box-sizing: border-box;
    }

    th.el-table__cell {
      background-color: #FFF;
      background: #F6F8FA;
      color: #364359;
      line-height: 16px;
      font-weight: normal;
    }
  }
}

.el-table__fixed {
  height: 100% !important;
}

.el-table__fixed-right {
  height: 100% !important;
}

// 空数据
.el-table .el-table__empty-block {
  width: 100% !important;
}

// 空数据
.el-table .el-table__empty-block {
  width: 100% !important;
}

.el-tooltip__popper {
  max-width: 30%;
}

// 解决固定列下滚动条被遮挡问题
.el-table__fixed, .el-table__fixed-right {
  //height: auto !important; // 让固定列的高自适应，且设置!important覆盖ele-ui的默认样式
  //bottom: 8px !important; // 固定列默认设置了定位，position: absolute;top: 0;left: 0;只需要再设置一下bottom的值，让固定列和父元素的底部出现距离即可
}

// 解决表格滚动到底部时固定列与其余列错位问题
.el-table__fixed-body-wrapper {
  //height: calc(100% - 130px) !important;
  //padding-bottom: 17px;
  //box-sizing: content-box;
}

// 表格右侧固定栏底部横线隐藏
.el-table__fixed-right::before {
  display: none;
}


//日期禁用的背景色
.el-date-table__row {
  .disabled {
    background: #fff !important;
  }
}

.el-year-table {
  .disabled {
    background: #fff !important;
  }
}

.el-table__body .hover-row > td, {
  background-color: #d1d2d3 !important;
}


.el-table__body .current-row > td, {
  background-color: #d1d2d3 !important;
}

// 树
.el-tree-node__content {
  //min-height: 36px !important;
  height: auto;
  white-space: normal !important;
  word-break: break-word; /* 对于英文和数字，强制在单词内换行 */
  word-wrap: break-word; /* 对于英文和数字，允许在单词边界换行，对于中文等字符则自然换行 */
  overflow-wrap: break-word; /* 等同于word-wrap，*/
  line-height: 1.5; /* 可根据需要设置合适的行高 */

  .el-tree-node__expand-icon {
    font-size: 18px;
  }

}

// 弹框样式
.el-dialog__wrapper {
  .el-dialog__header {
    height: 54px;
    padding-bottom: 20px;
    background: #F6F8FA;
    display: flex;
    align-items: center;
    line-height: 22px;

    .el-dialog__title {
      font-weight: 500;
      font-size: 16px;
      color: #364359;
    }
  }

  .el-dialog__footer {
    padding-top: 20px;
    border-top: 1px solid #E9ECF1;
  }

  .el-dialog__body {
    overflow: auto;
  }
}

// 滚动条样式

::-webkit-scrollbar {
  width: 1px;
  height: 10px;
  border-radius: 15px;
  -webkit-border-radius: 15px;
}

::-webkit-scrollbar-track-piece {
  background-color: #ffff;
  border-radius: 15px;
  -webkit-border-radius: 15px;
}

::-webkit-scrollbar-thumb:vertical {
  height: 5px;
  background-color: rgba(144, 147, 153, 0.5);
  border-radius: 15px;
  -webkit-border-radius: 15px;
}

::-webkit-scrollbar-thumb:horizontal {
  width: 5px;
  background-color: rgba(144, 147, 153, 0.5);
  border-radius: 15px;
  -webkit-border-radius: 15px;
}

/* 设置滚动条轨道（Y轴）样式 */
::-webkit-scrollbar-track-piece {
  background-color: transparent;
  border-radius: 15px;
  -webkit-border-radius: 15px;
}

/* 设置滚动条滑块（Y轴）样式 */
::-webkit-scrollbar-thumb:vertical {
  height: 10px; /* 修改为所需的垂直滚动条滑块高度 */
  background-color: transparent;
  border-radius: 15px;
  -webkit-border-radius: 15px;
}

// 抽屉样式
.el-drawer__body {
  padding-right: 50px;
}
