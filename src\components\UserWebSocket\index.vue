<template>
  <div>
    <!--<el-button plain @click="open" />-->
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import WebSocketClient from '@/utils/webSocketClient'

export default {
  name: 'UserWebSocket',
  data() {
    return {
      ws: null
    }
  },
  computed: {
    ...mapGetters([
      'user',
      'token'
    ])
  },
  watch: {
    token(newToken) {
      if (newToken) {
        this.initWebSocket();
      }
    }
  },
  created() {
    if (this.token) {
      this.initWebSocket();
    }
  },
  beforeDestroy() {
    this.closeSocket()
  },
  methods: {
    closeSocket() {
      // 关闭WebSocket连接
      if (this.ws) {
        this.ws.ws.close();
      }
    },
    initWebSocket() {
      setTimeout(() => {
        const { id } = this.user
        const wsUri = process.env.VUE_APP_WS_API + `/webSocket/${id}`;
        this.ws = new WebSocketClient(wsUri, {
          onmessage: this.handleMessage
        });
        console.log(this.ws, '<===>', 'this.ws')
      }, 2000)
    },
    // 处理接收到的消息
    handleMessage(e) {
      console.log(e, '<===>', 'e')
      let data = {}
      if (e == 'ping') {
        return
      } else {
        data = JSON.parse(e)
      }
      if (data.msgType === 'INFO') {
        this.$notify({
          title: '',
          message: data.msg,
          type: 'success',
          dangerouslyUseHTMLString: true,
          duration: 0
        })
      } else if (data.msgType === 'ERROR') {
        this.$notify({
          title: '',
          message: data.msg,
          dangerouslyUseHTMLString: true,
          type: 'error',
          duration: 0
        })
      }
    },
    open() {
      this.$notify({
        title: '出问题了！！！',
        dangerouslyUseHTMLString: true,
        duration: 0,
        message: `
          <div id="notify-content">
            <strong style="display: block;">以下是有问题的附件</strong>
            <br>
            <button type="button" class="el-button el-button--primary el-button--mini" id="pre-notify-button">预览</button>
            <button type="button" class="el-button el-button--success el-button--mini" id="dow-notify-button">下载</button>
          </div>
        `,
        onClose: () => {
          // 在通知关闭时，移除事件监听器
          document.getElementById('pre-notify-button').removeEventListener('click', this.handlePre);
          document.getElementById('dow-notify-button').removeEventListener('click', this.handleDown);
        }
      });

      // 添加事件监听器到按钮
      this.$nextTick(() => {
        document.getElementById('pre-notify-button').addEventListener('click', this.handlePre);
        document.getElementById('dow-notify-button').addEventListener('click', this.handleDown);
      });
    },
    handleDown() {
      this.$message('下载按钮被点击了！');
    },
    handlePre() {
      this.$message('预览按钮被点击了！');
    }
  }
}
</script>
