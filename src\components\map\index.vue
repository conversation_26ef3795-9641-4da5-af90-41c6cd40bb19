<template>
  <div id="mapContainer" />
</template>

<script>
/* eslint-disable */
var mapScript = function () {
	return new Promise((resolve, reject) => {
		var script = document.createElement('script');
		script.src = 'https://map.qq.com/api/gljs?v=1.exp&libraries=service&key=DRSBZ-FGF66-GWASH-EBN6A-SL7PH-7CFV7';
		script.charset = 'utf-8';
		script.id = 'qqMap';
		script.onload = script.onreadystatechange = () => {
			resolve('成功');
			script.onload = script.onreadystatechange = null;
		};
		document.getElementById('app').appendChild(script);
	});
};
import Bus from '@/utils/bus';

export default {
	name: 'mapContainer',
	data() {
		return {
			map: null
		}
	},
	props: {
		busEventName: {
			type: String,
			default: () => 'sendBybus'
		}
	},
	created() {
	},
	mounted() {
		if (!document.getElementById('qqMap')) {
			mapScript().then(res => {
				this.initData();
			});
		} else {
			var time = setInterval(() => {
				if (window.TMap) {
					this.initData();
					clearInterval(time);
				}
			}, 500);
		}
	},
	beforeDestroy() {
		if (this.map) {
			this.map.destroy();
			this.map = null;
		}
	},
	methods: {
		initData(type = 'vector', features) {
			if (this.map) {
				this.map.destroy();
				this.map = null;
			}
			// 定义地图中心点坐标[, ]
			var center = new TMap.LatLng(40.128936, 116.653525);
			// 定义map变量，调用 TMap.Map() 构造函数创建地图
			this.map = new TMap.Map(document.getElementById('mapContainer'), {
				center: center, // 设置地图中心点坐标
				zoom: 11, // 设置地图缩放级别
				minZoom: 10,
				maxZoom: 20,
				baseMap: {
					type,
					features
				},
				rotation: 0 // 设置地图旋转角度
			});
			Bus.$emit(this.busEventName, this.map);//兄弟组件传参
			//画顺义区边界
			this.findBorder();
		},
		findBorder() {
			var district = new TMap.service.District({
				// 新建一个行政区划类
				polygon: 2, // 返回行政区划边界的类型
			});
			district
					.search({keyword: '110113'})
					.then((result) => {
						// 搜索行政区划信息
						result.result.forEach((level) => {
							level.forEach((place) => {
								var bounds = [];
								var newGeometries = place.polygon.map((polygon, index) => {
									bounds.push(this.fitBounds(polygon)); // 计算能完整呈现行政区边界的最小矩形范围
									return {
										id: `${ place.id }_${ index }`,
										paths: polygon, // 将得到的行政区划边界用多边形标注在地图上
										styleId: 'polygon', // 绑定样式名
									};
								});
								bounds = bounds.reduce((a, b) => {
									return this.fitBounds([
										a.getNorthEast(),
										a.getSouthWest(),
										b.getNorthEast(),
										b.getSouthWest(),
									]);
								}); // 若一行政区有多个多边形边界，应计算能包含所有多边形边界的范围。
								this.setBounde(newGeometries);
							});
						});
					})
					.catch((error) => {
						console.log(error)
					});
		},
		fitBounds(latLngList) {
			// 由多边形顶点坐标数组计算能完整呈现该多边形的最小矩形范围
			if (latLngList.length === 0) {
				return null;
			}
			var boundsN = latLngList[0].getLat();
			var boundsS = boundsN;
			var boundsW = latLngList[0].getLng();
			var boundsE = boundsW;
			latLngList.forEach((point) => {
				point.getLat() > boundsN && (boundsN = point.getLat());
				point.getLat() < boundsS && (boundsS = point.getLat());
				point.getLng() > boundsE && (boundsE = point.getLng());
				point.getLng() < boundsW && (boundsW = point.getLng());
			});
			return new TMap.LatLngBounds(
					new TMap.LatLng(boundsS, boundsW),
					new TMap.LatLng(boundsN, boundsE)
			);
		},
		setBounde(newGeometries) {
			var polygons = new TMap.MultiPolygon({
				id: 'polygon-layer', // 图层id
				map: this.map, // 设置多边形图层显示到哪个地图实例中
				// 多边形样式
				styles: {
					polygon: new TMap.PolygonStyle({
						color: 'rgba(255, 255, 255, 0)', // 面填充色
						showBorder: true, // 是否显示拔起面的边线
						borderWidth: 2,
						borderColor: '#3777FF' // 边线颜色
					})
				},
				// 多边形数据
				geometries: newGeometries
			});
		}
	}
}
</script>

<style scoped>
#mapContainer {
	height: calc(100vh - 150px);
}

.map-box {
	position: relative;
}

.search-box {
	position: absolute;
	width: 100%;
	left: 20px;
	top: 20px;
	z-index: 2000;
	padding: 10px 0 0 10px;
}
</style>
