import request from '@/utils/request'
// import qs from 'qs'
// 查询模板
export function get(params) {
  return request({
    url: 'api/digitalFieldDeptDetail',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/digitalFieldDeptDetail',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/digitalFieldDeptDetail',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/digitalFieldDeptDetail',
    method: 'put',
    data
  })
}

export default { get, add, edit, del }
