import OSS from 'ali-oss';
import { getConfig } from '@/utils/getConfigData';

const REGION = 'oss-cn-beijing';
const SECURE = true;
let ossClient = null
export let aliyunConfig = {}

export function getOssClient() {
  const query = {
    type: 'STORAGE',
    key: 'ALIYUN-oss-1'
  }
  return getConfig(query).then(res => {
    const data = res.extend.data
    aliyunConfig = data;
    return data
  })
}

export function initOss() {
  return getOssClient().then(data => {
    const { accessKey, secretKey, bucketName } = data;
    ossClient = new OSS({
      region: REGION,
      accessKeyId: accessKey,
      accessKeySecret: secretKey,
      bucket: bucketName,
      secure: SECURE // 是否使用 HTTPS，默认为 false
    });
    return ossClient// Return the initialized ossClient
  });
}

export default initOss;
