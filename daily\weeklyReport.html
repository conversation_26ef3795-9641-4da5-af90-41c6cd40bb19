<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>项目周报</title>
    <style>
        body, div, table, thead, tbody, tfoot, tr, th, td, p {
            font-family: "宋体";
            font-size: small
        }

        .td-tblr {
            border: 1px solid #000000;
        }

        .break-content {
            white-space: pre-wrap;
            text-align: left;
        }

        .horizontal-line {
            /* border-top: 1px solid #000000; */
            margin: 0;
        }
    </style>
    <script>
			function getUrlParams() {
				const url = window.location.href;
				const queryString = url.split('?')[1];
				if (!queryString) return {};

				const params = {};
				queryString.split('&').forEach(param => {
					const [key, value] = param.split('=');
					params[decodeURIComponent(key)] = decodeURIComponent(value);
				});
				return params;
			}

			function setPageTitle(params) {
				const title = `${ params.weekIndex }项目周报` || '项目周报';
				const titleElement = document.getElementById('pageTitle');
				if (titleElement) {
					titleElement.textContent = title;
				}
			}

			window.onload = function () {
				const urlParams = getUrlParams();
				setPageTitle(urlParams);
			};
    </script>
</head>
<body>
<h2 id="pageTitle" style="text-align:center;">
</h2>
<table border="0" ellspacing="0" style='border-collapse: collapse;'>
    <colgroup>
        <col width="360">
        <col width="100">
        <col width="200">
        <col width="350">
        <col width="350">
        <col width="350">
        <col width="200">
    </colgroup>
    <tr>
        <th align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle>项目</th>
        <th align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle>负责人</th>
        <th align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle>上周需要协调的问题</th>
        <th align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle>上周工作计划</th>
        <th align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle>本周工作情况</th>
        <th align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle>下周工作计划</th>
        <th align="center" bgcolor="#BFBFBF" class="td-tblr" valign=middle>需要协调的问题</th>
    </tr>
    #for(x : oaPmWeeklyReport.mergeReport(mixQuery.weeklyReport??,pageable))
    <tr>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle>#(x.key[0])</td>
        <td align="center" bgcolor="#FFFFFF" class="td-tblr" valign=middle>#(x.key[1])</td>

        <td align="center" bgcolor="#FFFFFF" class="td-tblr break-content" valign=top>
            #for(y: x.value["上周需要协调的问题"]??)
            <strong>#(y.fv3??) - #(y.fv4??) #if(y.fv11??) (第#(y.fv11??)周) #end</strong>
            <br>
            #(y.fv7) <br>
            #(y.fv8.replaceAll("\n", "<br/>"))
            <br>
            <div class="horizontal-line"></div>
            #end
        </td>

        <td align="center" bgcolor="#FFFFFF" class="td-tblr break-content" valign=top>
            #for(y: x.value["上周工作计划"]??)
            <strong>#(y.fv3??) - #(y.fv4??) #if(y.fv11??) (第#(y.fv11??)周) #end</strong>
            <br>
            #(y.fv7) <br>
            #(y.fv8.replaceAll("\n", "<br/>"))
            <br>
            <div class="horizontal-line"></div>
            #end
        </td>

        <td align="center" bgcolor="#FFFFFF" class="td-tblr break-content" valign=top>
            #for(y: x.value["本周工作情况"]??)
            <strong>#(y.fv3??) - #(y.fv4??) #if(y.fv11??) (第#(y.fv11??)周) #end</strong>
            <br>
            #(y.fv7) <br>
            #(y.fv8.replaceAll("\n", "<br/>"))
            <br>
            <div class="horizontal-line"></div>
            #end
        </td>

        <td align="center" bgcolor="#FFFFFF" class="td-tblr break-content" valign=top>
            #for(y: x.value["下周工作计划"]??)
            <strong>#(y.fv3??) - #(y.fv4??) #if(y.fv11??) (第#(y.fv11??)周) #end</strong>
            <br>
            #(y.fv7) <br>
            #(y.fv8.replaceAll("\n", "<br/>"))
            <br>
            <div class="horizontal-line"></div>
            #end
        </td>


        <td align="center" bgcolor="#FFFFFF" class="td-tblr break-content" valign=top>
            #for(y: x.value["需要协调的问题"]??)
            <strong>#(y.fv3??) - #(y.fv4??)
                    #if(y.fv11??) (第#(y.fv11??)周) #end</strong>
            <br>
            #(y.fv7) <br>
            #(y.fv8.replaceAll("\n", "<br/>"))
            <br>
            <div class="horizontal-line"></div>
            #end
        </td>


    </tr>
    #end
</table>

</body>
</html>
